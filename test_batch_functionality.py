#!/usr/bin/env python3
"""
Test script to verify batch processing functionality works correctly
"""

import random
from bgl_log_classification import (
    Config, 
    create_train_val_test_split, 
    predict_batch,
    create_prompt
)

def test_batch_processing():
    """Test that batch processing produces correct results"""
    print("Testing batch processing functionality...")
    
    # Create some test data
    print("Creating test data...")
    _, _, test_samples = create_train_val_test_split(
        Config.DATASET_PATH,
        total_sample_size=100,  # Small sample for testing
        train_ratio=0.8,
        val_ratio=0.1,
        test_ratio=0.1
    )
    
    print(f"Created {len(test_samples)} test samples")
    
    # Test that we can create prompts
    print("\nTesting prompt creation...")
    sample_content = test_samples[0][1]
    prompt = create_prompt(sample_content)
    print(f"Sample prompt: {prompt[:100]}...")
    
    # Test batch processing without model (just check the structure)
    print("\nTesting batch processing structure...")
    log_contents = [content for _, content in test_samples[:5]]
    
    print(f"Testing with {len(log_contents)} log entries")
    print("Sample log contents:")
    for i, content in enumerate(log_contents[:3]):
        print(f"  {i+1}: {content[:80]}...")
    
    # Test different batch sizes
    batch_sizes = [1, 2, 5]
    
    print(f"\nTesting batch size configurations...")
    for batch_size in batch_sizes:
        print(f"  Batch size {batch_size}: Would process in {len(log_contents)//batch_size + (1 if len(log_contents)%batch_size else 0)} batches")
    
    print("\n✅ Batch processing structure test PASSED!")
    print("\nNote: To test with actual model predictions, run:")
    print("  python test_batch_performance.py")
    
    return True

if __name__ == "__main__":
    success = test_batch_processing()
    if success:
        print("\n🎉 Batch functionality test completed successfully!")
    else:
        print("\n❌ Batch functionality test failed!")
