{"best_global_step": 3000, "best_metric": 0.5090791583061218, "best_model_checkpoint": "./bgl_log_classifier/checkpoint-3000", "epoch": 3.0, "eval_steps": 500, "global_step": 3330, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.009009009009009009, "grad_norm": 2.219428777694702, "learning_rate": 0.0001997593984962406, "loss": 3.4066, "step": 10}, {"epoch": 0.018018018018018018, "grad_norm": 1.387972116470337, "learning_rate": 0.00019915789473684212, "loss": 1.6166, "step": 20}, {"epoch": 0.02702702702702703, "grad_norm": 0.9073598980903625, "learning_rate": 0.00019855639097744363, "loss": 1.2788, "step": 30}, {"epoch": 0.036036036036036036, "grad_norm": 1.1476634740829468, "learning_rate": 0.00019795488721804512, "loss": 1.1248, "step": 40}, {"epoch": 0.04504504504504504, "grad_norm": 0.7822989821434021, "learning_rate": 0.00019735338345864664, "loss": 1.1047, "step": 50}, {"epoch": 0.05405405405405406, "grad_norm": 1.097267508506775, "learning_rate": 0.00019675187969924813, "loss": 1.0034, "step": 60}, {"epoch": 0.06306306306306306, "grad_norm": 0.8312771320343018, "learning_rate": 0.00019615037593984964, "loss": 0.8835, "step": 70}, {"epoch": 0.07207207207207207, "grad_norm": 0.7873963713645935, "learning_rate": 0.00019554887218045116, "loss": 0.9149, "step": 80}, {"epoch": 0.08108108108108109, "grad_norm": 0.8115925192832947, "learning_rate": 0.00019494736842105265, "loss": 0.873, "step": 90}, {"epoch": 0.09009009009009009, "grad_norm": 0.7134601473808289, "learning_rate": 0.00019434586466165414, "loss": 0.8862, "step": 100}, {"epoch": 0.0990990990990991, "grad_norm": 0.8807268738746643, "learning_rate": 0.00019374436090225565, "loss": 0.7893, "step": 110}, {"epoch": 0.10810810810810811, "grad_norm": 0.7914533615112305, "learning_rate": 0.00019314285714285717, "loss": 0.8456, "step": 120}, {"epoch": 0.11711711711711711, "grad_norm": 0.5444416999816895, "learning_rate": 0.00019254135338345866, "loss": 0.7603, "step": 130}, {"epoch": 0.12612612612612611, "grad_norm": 0.7394588589668274, "learning_rate": 0.00019193984962406015, "loss": 0.8286, "step": 140}, {"epoch": 0.13513513513513514, "grad_norm": 0.546661913394928, "learning_rate": 0.00019133834586466166, "loss": 0.7702, "step": 150}, {"epoch": 0.14414414414414414, "grad_norm": 0.8735962510108948, "learning_rate": 0.00019073684210526318, "loss": 0.7572, "step": 160}, {"epoch": 0.15315315315315314, "grad_norm": 0.7658746838569641, "learning_rate": 0.00019013533834586467, "loss": 0.7608, "step": 170}, {"epoch": 0.16216216216216217, "grad_norm": 0.7507514953613281, "learning_rate": 0.00018953383458646616, "loss": 0.7828, "step": 180}, {"epoch": 0.17117117117117117, "grad_norm": 0.9592691659927368, "learning_rate": 0.00018893233082706767, "loss": 0.7657, "step": 190}, {"epoch": 0.18018018018018017, "grad_norm": 0.618772566318512, "learning_rate": 0.0001883308270676692, "loss": 0.7611, "step": 200}, {"epoch": 0.1891891891891892, "grad_norm": 0.6731516718864441, "learning_rate": 0.00018772932330827068, "loss": 0.7821, "step": 210}, {"epoch": 0.1981981981981982, "grad_norm": 0.5899109244346619, "learning_rate": 0.0001871278195488722, "loss": 0.6822, "step": 220}, {"epoch": 0.2072072072072072, "grad_norm": 0.5841113924980164, "learning_rate": 0.00018652631578947368, "loss": 0.7403, "step": 230}, {"epoch": 0.21621621621621623, "grad_norm": 0.5319094061851501, "learning_rate": 0.0001859248120300752, "loss": 0.6986, "step": 240}, {"epoch": 0.22522522522522523, "grad_norm": 0.7457212805747986, "learning_rate": 0.0001853233082706767, "loss": 0.7358, "step": 250}, {"epoch": 0.23423423423423423, "grad_norm": 0.56687992811203, "learning_rate": 0.0001847218045112782, "loss": 0.7036, "step": 260}, {"epoch": 0.24324324324324326, "grad_norm": 0.8039962649345398, "learning_rate": 0.00018412030075187972, "loss": 0.7037, "step": 270}, {"epoch": 0.25225225225225223, "grad_norm": 0.5153774619102478, "learning_rate": 0.0001835187969924812, "loss": 0.6695, "step": 280}, {"epoch": 0.26126126126126126, "grad_norm": 0.693912148475647, "learning_rate": 0.0001829172932330827, "loss": 0.725, "step": 290}, {"epoch": 0.2702702702702703, "grad_norm": 0.6976706385612488, "learning_rate": 0.0001823157894736842, "loss": 0.6814, "step": 300}, {"epoch": 0.27927927927927926, "grad_norm": 0.55686354637146, "learning_rate": 0.00018171428571428573, "loss": 0.6727, "step": 310}, {"epoch": 0.2882882882882883, "grad_norm": 0.7632626891136169, "learning_rate": 0.00018111278195488724, "loss": 0.6911, "step": 320}, {"epoch": 0.2972972972972973, "grad_norm": 0.5521382093429565, "learning_rate": 0.0001805112781954887, "loss": 0.6595, "step": 330}, {"epoch": 0.3063063063063063, "grad_norm": 0.7134902477264404, "learning_rate": 0.00017990977443609022, "loss": 0.6619, "step": 340}, {"epoch": 0.3153153153153153, "grad_norm": 0.6679260730743408, "learning_rate": 0.00017930827067669174, "loss": 0.6442, "step": 350}, {"epoch": 0.32432432432432434, "grad_norm": 0.5407384037971497, "learning_rate": 0.00017870676691729325, "loss": 0.632, "step": 360}, {"epoch": 0.3333333333333333, "grad_norm": 0.7612490057945251, "learning_rate": 0.00017810526315789474, "loss": 0.7038, "step": 370}, {"epoch": 0.34234234234234234, "grad_norm": 0.5388392210006714, "learning_rate": 0.00017750375939849623, "loss": 0.6665, "step": 380}, {"epoch": 0.35135135135135137, "grad_norm": 0.7052571773529053, "learning_rate": 0.00017690225563909775, "loss": 0.6539, "step": 390}, {"epoch": 0.36036036036036034, "grad_norm": 0.5152245163917542, "learning_rate": 0.00017630075187969926, "loss": 0.6659, "step": 400}, {"epoch": 0.36936936936936937, "grad_norm": 0.8231921792030334, "learning_rate": 0.00017569924812030075, "loss": 0.6967, "step": 410}, {"epoch": 0.3783783783783784, "grad_norm": 0.5214396715164185, "learning_rate": 0.00017509774436090227, "loss": 0.6465, "step": 420}, {"epoch": 0.38738738738738737, "grad_norm": 0.6900960803031921, "learning_rate": 0.00017449624060150376, "loss": 0.6298, "step": 430}, {"epoch": 0.3963963963963964, "grad_norm": 0.5449349284172058, "learning_rate": 0.00017389473684210527, "loss": 0.6446, "step": 440}, {"epoch": 0.40540540540540543, "grad_norm": 0.7809153199195862, "learning_rate": 0.00017329323308270676, "loss": 0.6491, "step": 450}, {"epoch": 0.4144144144144144, "grad_norm": 0.616826057434082, "learning_rate": 0.00017269172932330828, "loss": 0.6123, "step": 460}, {"epoch": 0.42342342342342343, "grad_norm": 0.5962993502616882, "learning_rate": 0.0001720902255639098, "loss": 0.6497, "step": 470}, {"epoch": 0.43243243243243246, "grad_norm": 0.4309585988521576, "learning_rate": 0.00017148872180451128, "loss": 0.6257, "step": 480}, {"epoch": 0.44144144144144143, "grad_norm": 0.5664756894111633, "learning_rate": 0.00017088721804511277, "loss": 0.6103, "step": 490}, {"epoch": 0.45045045045045046, "grad_norm": 0.5345722436904907, "learning_rate": 0.0001702857142857143, "loss": 0.7007, "step": 500}, {"epoch": 0.45045045045045046, "eval_loss": 0.6174425482749939, "eval_runtime": 10.8719, "eval_samples_per_second": 90.784, "eval_steps_per_second": 45.438, "step": 500}, {"epoch": 0.4594594594594595, "grad_norm": 0.5399738550186157, "learning_rate": 0.0001696842105263158, "loss": 0.6251, "step": 510}, {"epoch": 0.46846846846846846, "grad_norm": 0.6852405071258545, "learning_rate": 0.00016908270676691732, "loss": 0.6214, "step": 520}, {"epoch": 0.4774774774774775, "grad_norm": 0.6170178651809692, "learning_rate": 0.00016848120300751878, "loss": 0.6304, "step": 530}, {"epoch": 0.4864864864864865, "grad_norm": 0.5317628383636475, "learning_rate": 0.0001678796992481203, "loss": 0.6084, "step": 540}, {"epoch": 0.4954954954954955, "grad_norm": 0.6856704354286194, "learning_rate": 0.00016727819548872182, "loss": 0.5945, "step": 550}, {"epoch": 0.5045045045045045, "grad_norm": 0.35659343004226685, "learning_rate": 0.00016667669172932333, "loss": 0.6229, "step": 560}, {"epoch": 0.5135135135135135, "grad_norm": 0.45893022418022156, "learning_rate": 0.00016607518796992482, "loss": 0.6935, "step": 570}, {"epoch": 0.5225225225225225, "grad_norm": 0.6129117608070374, "learning_rate": 0.0001654736842105263, "loss": 0.6282, "step": 580}, {"epoch": 0.5315315315315315, "grad_norm": 0.6074191927909851, "learning_rate": 0.00016487218045112783, "loss": 0.5856, "step": 590}, {"epoch": 0.5405405405405406, "grad_norm": 0.5069369077682495, "learning_rate": 0.00016427067669172934, "loss": 0.6148, "step": 600}, {"epoch": 0.5495495495495496, "grad_norm": 0.509436845779419, "learning_rate": 0.00016366917293233083, "loss": 0.6137, "step": 610}, {"epoch": 0.5585585585585585, "grad_norm": 0.6873916387557983, "learning_rate": 0.00016306766917293235, "loss": 0.6262, "step": 620}, {"epoch": 0.5675675675675675, "grad_norm": 0.4064469635486603, "learning_rate": 0.00016246616541353384, "loss": 0.5849, "step": 630}, {"epoch": 0.5765765765765766, "grad_norm": 0.39587685465812683, "learning_rate": 0.00016186466165413535, "loss": 0.6093, "step": 640}, {"epoch": 0.5855855855855856, "grad_norm": 0.5129104852676392, "learning_rate": 0.00016126315789473684, "loss": 0.5591, "step": 650}, {"epoch": 0.5945945945945946, "grad_norm": 0.5190238952636719, "learning_rate": 0.00016066165413533836, "loss": 0.6205, "step": 660}, {"epoch": 0.6036036036036037, "grad_norm": 0.4888823330402374, "learning_rate": 0.00016006015037593987, "loss": 0.5942, "step": 670}, {"epoch": 0.6126126126126126, "grad_norm": 0.5382779240608215, "learning_rate": 0.00015945864661654136, "loss": 0.5967, "step": 680}, {"epoch": 0.6216216216216216, "grad_norm": 0.5592765808105469, "learning_rate": 0.00015885714285714285, "loss": 0.5838, "step": 690}, {"epoch": 0.6306306306306306, "grad_norm": 0.48280587792396545, "learning_rate": 0.00015825563909774437, "loss": 0.5925, "step": 700}, {"epoch": 0.6396396396396397, "grad_norm": 0.4155188202857971, "learning_rate": 0.00015765413533834588, "loss": 0.5851, "step": 710}, {"epoch": 0.6486486486486487, "grad_norm": 0.63788241147995, "learning_rate": 0.00015705263157894737, "loss": 0.5829, "step": 720}, {"epoch": 0.6576576576576577, "grad_norm": 1.1654767990112305, "learning_rate": 0.00015645112781954886, "loss": 0.6644, "step": 730}, {"epoch": 0.6666666666666666, "grad_norm": 0.7589302659034729, "learning_rate": 0.00015584962406015038, "loss": 0.6494, "step": 740}, {"epoch": 0.6756756756756757, "grad_norm": 0.7932037711143494, "learning_rate": 0.0001552481203007519, "loss": 0.5998, "step": 750}, {"epoch": 0.6846846846846847, "grad_norm": 0.6680371165275574, "learning_rate": 0.0001546466165413534, "loss": 0.6371, "step": 760}, {"epoch": 0.6936936936936937, "grad_norm": 0.43630582094192505, "learning_rate": 0.0001540451127819549, "loss": 0.5993, "step": 770}, {"epoch": 0.7027027027027027, "grad_norm": 0.4234333634376526, "learning_rate": 0.00015344360902255639, "loss": 0.6029, "step": 780}, {"epoch": 0.7117117117117117, "grad_norm": 0.5340341925621033, "learning_rate": 0.0001528421052631579, "loss": 0.5982, "step": 790}, {"epoch": 0.7207207207207207, "grad_norm": 0.3913106322288513, "learning_rate": 0.00015224060150375942, "loss": 0.6118, "step": 800}, {"epoch": 0.7297297297297297, "grad_norm": 0.7050166130065918, "learning_rate": 0.0001516390977443609, "loss": 0.608, "step": 810}, {"epoch": 0.7387387387387387, "grad_norm": 0.7115857005119324, "learning_rate": 0.00015103759398496242, "loss": 0.6051, "step": 820}, {"epoch": 0.7477477477477478, "grad_norm": 0.552038311958313, "learning_rate": 0.0001504360902255639, "loss": 0.5831, "step": 830}, {"epoch": 0.7567567567567568, "grad_norm": 0.5485140681266785, "learning_rate": 0.00014983458646616543, "loss": 0.606, "step": 840}, {"epoch": 0.7657657657657657, "grad_norm": 0.5860025882720947, "learning_rate": 0.00014923308270676692, "loss": 0.5889, "step": 850}, {"epoch": 0.7747747747747747, "grad_norm": 0.6137528419494629, "learning_rate": 0.00014863157894736843, "loss": 0.6168, "step": 860}, {"epoch": 0.7837837837837838, "grad_norm": 0.6291046142578125, "learning_rate": 0.00014803007518796995, "loss": 0.6081, "step": 870}, {"epoch": 0.7927927927927928, "grad_norm": 0.7925742268562317, "learning_rate": 0.00014742857142857144, "loss": 0.5998, "step": 880}, {"epoch": 0.8018018018018018, "grad_norm": 0.4998583495616913, "learning_rate": 0.00014682706766917293, "loss": 0.5662, "step": 890}, {"epoch": 0.8108108108108109, "grad_norm": 0.47474637627601624, "learning_rate": 0.00014622556390977444, "loss": 0.5702, "step": 900}, {"epoch": 0.8198198198198198, "grad_norm": 0.4407787322998047, "learning_rate": 0.00014562406015037596, "loss": 0.5888, "step": 910}, {"epoch": 0.8288288288288288, "grad_norm": 0.45542994141578674, "learning_rate": 0.00014502255639097745, "loss": 0.5515, "step": 920}, {"epoch": 0.8378378378378378, "grad_norm": 0.47670137882232666, "learning_rate": 0.00014442105263157894, "loss": 0.5885, "step": 930}, {"epoch": 0.8468468468468469, "grad_norm": 0.40780383348464966, "learning_rate": 0.00014381954887218045, "loss": 0.57, "step": 940}, {"epoch": 0.8558558558558559, "grad_norm": 0.500187873840332, "learning_rate": 0.00014321804511278197, "loss": 0.5708, "step": 950}, {"epoch": 0.8648648648648649, "grad_norm": 0.3474435806274414, "learning_rate": 0.00014261654135338346, "loss": 0.5644, "step": 960}, {"epoch": 0.8738738738738738, "grad_norm": 0.44873785972595215, "learning_rate": 0.00014201503759398497, "loss": 0.5935, "step": 970}, {"epoch": 0.8828828828828829, "grad_norm": 0.7072736620903015, "learning_rate": 0.00014141353383458646, "loss": 0.5735, "step": 980}, {"epoch": 0.8918918918918919, "grad_norm": 0.5049567222595215, "learning_rate": 0.00014081203007518798, "loss": 0.584, "step": 990}, {"epoch": 0.9009009009009009, "grad_norm": 0.44630321860313416, "learning_rate": 0.00014021052631578947, "loss": 0.5593, "step": 1000}, {"epoch": 0.9009009009009009, "eval_loss": 0.5656346678733826, "eval_runtime": 10.9227, "eval_samples_per_second": 90.362, "eval_steps_per_second": 45.227, "step": 1000}, {"epoch": 0.9099099099099099, "grad_norm": 0.3168415129184723, "learning_rate": 0.00013960902255639098, "loss": 0.6083, "step": 1010}, {"epoch": 0.918918918918919, "grad_norm": 0.38495466113090515, "learning_rate": 0.0001390075187969925, "loss": 0.5883, "step": 1020}, {"epoch": 0.9279279279279279, "grad_norm": 0.5379432439804077, "learning_rate": 0.000138406015037594, "loss": 0.5706, "step": 1030}, {"epoch": 0.9369369369369369, "grad_norm": 0.4421406388282776, "learning_rate": 0.0001378045112781955, "loss": 0.5849, "step": 1040}, {"epoch": 0.9459459459459459, "grad_norm": 0.5512763261795044, "learning_rate": 0.000137203007518797, "loss": 0.5745, "step": 1050}, {"epoch": 0.954954954954955, "grad_norm": 0.5395145416259766, "learning_rate": 0.0001366015037593985, "loss": 0.5733, "step": 1060}, {"epoch": 0.963963963963964, "grad_norm": 0.48479005694389343, "learning_rate": 0.00013600000000000003, "loss": 0.5833, "step": 1070}, {"epoch": 0.972972972972973, "grad_norm": 0.45725274085998535, "learning_rate": 0.00013539849624060151, "loss": 0.5938, "step": 1080}, {"epoch": 0.9819819819819819, "grad_norm": 0.4861120283603668, "learning_rate": 0.000134796992481203, "loss": 0.58, "step": 1090}, {"epoch": 0.990990990990991, "grad_norm": 0.5448392033576965, "learning_rate": 0.00013419548872180452, "loss": 0.5769, "step": 1100}, {"epoch": 1.0, "grad_norm": 0.640223503112793, "learning_rate": 0.00013359398496240604, "loss": 0.567, "step": 1110}, {"epoch": 1.009009009009009, "grad_norm": 0.3832101821899414, "learning_rate": 0.00013299248120300752, "loss": 0.5565, "step": 1120}, {"epoch": 1.018018018018018, "grad_norm": 0.6612333059310913, "learning_rate": 0.00013239097744360901, "loss": 0.52, "step": 1130}, {"epoch": 1.027027027027027, "grad_norm": 0.4706147313117981, "learning_rate": 0.00013178947368421053, "loss": 0.5405, "step": 1140}, {"epoch": 1.0360360360360361, "grad_norm": 0.522296667098999, "learning_rate": 0.00013118796992481205, "loss": 0.566, "step": 1150}, {"epoch": 1.045045045045045, "grad_norm": 0.47931763529777527, "learning_rate": 0.00013058646616541353, "loss": 0.5823, "step": 1160}, {"epoch": 1.054054054054054, "grad_norm": 0.6199968457221985, "learning_rate": 0.00012998496240601505, "loss": 0.5454, "step": 1170}, {"epoch": 1.063063063063063, "grad_norm": 0.5497391819953918, "learning_rate": 0.00012938345864661654, "loss": 0.5677, "step": 1180}, {"epoch": 1.072072072072072, "grad_norm": 0.46456971764564514, "learning_rate": 0.00012878195488721806, "loss": 0.5502, "step": 1190}, {"epoch": 1.0810810810810811, "grad_norm": 0.6039029955863953, "learning_rate": 0.00012818045112781954, "loss": 0.5624, "step": 1200}, {"epoch": 1.09009009009009, "grad_norm": 0.36084893345832825, "learning_rate": 0.00012757894736842106, "loss": 0.5663, "step": 1210}, {"epoch": 1.0990990990990992, "grad_norm": 0.37229248881340027, "learning_rate": 0.00012697744360902258, "loss": 0.5317, "step": 1220}, {"epoch": 1.1081081081081081, "grad_norm": 0.5468270778656006, "learning_rate": 0.00012637593984962407, "loss": 0.5261, "step": 1230}, {"epoch": 1.117117117117117, "grad_norm": 0.4557114839553833, "learning_rate": 0.00012577443609022555, "loss": 0.5431, "step": 1240}, {"epoch": 1.1261261261261262, "grad_norm": 0.563366174697876, "learning_rate": 0.00012517293233082707, "loss": 0.5479, "step": 1250}, {"epoch": 1.135135135135135, "grad_norm": 0.4149091839790344, "learning_rate": 0.0001245714285714286, "loss": 0.5384, "step": 1260}, {"epoch": 1.1441441441441442, "grad_norm": 0.625213623046875, "learning_rate": 0.0001239699248120301, "loss": 0.531, "step": 1270}, {"epoch": 1.1531531531531531, "grad_norm": 0.6106917262077332, "learning_rate": 0.00012336842105263156, "loss": 0.5619, "step": 1280}, {"epoch": 1.1621621621621623, "grad_norm": 0.43631961941719055, "learning_rate": 0.00012276691729323308, "loss": 0.5397, "step": 1290}, {"epoch": 1.1711711711711712, "grad_norm": 0.4712531566619873, "learning_rate": 0.0001221654135338346, "loss": 0.5592, "step": 1300}, {"epoch": 1.1801801801801801, "grad_norm": 0.4095083177089691, "learning_rate": 0.0001215639097744361, "loss": 0.5437, "step": 1310}, {"epoch": 1.1891891891891893, "grad_norm": 0.4690212607383728, "learning_rate": 0.00012096240601503762, "loss": 0.5406, "step": 1320}, {"epoch": 1.1981981981981982, "grad_norm": 0.37759581208229065, "learning_rate": 0.00012036090225563909, "loss": 0.5635, "step": 1330}, {"epoch": 1.2072072072072073, "grad_norm": 0.39418232440948486, "learning_rate": 0.0001197593984962406, "loss": 0.4953, "step": 1340}, {"epoch": 1.2162162162162162, "grad_norm": 0.45451727509498596, "learning_rate": 0.00011915789473684211, "loss": 0.5349, "step": 1350}, {"epoch": 1.2252252252252251, "grad_norm": 0.46360647678375244, "learning_rate": 0.00011855639097744363, "loss": 0.5605, "step": 1360}, {"epoch": 1.2342342342342343, "grad_norm": 0.4214976131916046, "learning_rate": 0.00011795488721804513, "loss": 0.5577, "step": 1370}, {"epoch": 1.2432432432432432, "grad_norm": 0.4414446949958801, "learning_rate": 0.00011735338345864662, "loss": 0.5909, "step": 1380}, {"epoch": 1.2522522522522523, "grad_norm": 0.4497133493423462, "learning_rate": 0.00011675187969924812, "loss": 0.5715, "step": 1390}, {"epoch": 1.2612612612612613, "grad_norm": 0.3763071894645691, "learning_rate": 0.00011615037593984964, "loss": 0.5486, "step": 1400}, {"epoch": 1.2702702702702702, "grad_norm": 0.5737577676773071, "learning_rate": 0.00011554887218045114, "loss": 0.5628, "step": 1410}, {"epoch": 1.2792792792792793, "grad_norm": 0.31466928124427795, "learning_rate": 0.00011494736842105265, "loss": 0.5398, "step": 1420}, {"epoch": 1.2882882882882882, "grad_norm": 0.5945292115211487, "learning_rate": 0.00011434586466165413, "loss": 0.5681, "step": 1430}, {"epoch": 1.2972972972972974, "grad_norm": 0.533592164516449, "learning_rate": 0.00011374436090225565, "loss": 0.5336, "step": 1440}, {"epoch": 1.3063063063063063, "grad_norm": 0.5202757716178894, "learning_rate": 0.00011314285714285715, "loss": 0.5195, "step": 1450}, {"epoch": 1.3153153153153152, "grad_norm": 0.4658470153808594, "learning_rate": 0.00011254135338345866, "loss": 0.5179, "step": 1460}, {"epoch": 1.3243243243243243, "grad_norm": 0.47584110498428345, "learning_rate": 0.00011193984962406014, "loss": 0.5331, "step": 1470}, {"epoch": 1.3333333333333333, "grad_norm": 0.3497905135154724, "learning_rate": 0.00011133834586466166, "loss": 0.5283, "step": 1480}, {"epoch": 1.3423423423423424, "grad_norm": 0.4445066750049591, "learning_rate": 0.00011073684210526316, "loss": 0.5513, "step": 1490}, {"epoch": 1.3513513513513513, "grad_norm": 0.6007562875747681, "learning_rate": 0.00011013533834586467, "loss": 0.5148, "step": 1500}, {"epoch": 1.3513513513513513, "eval_loss": 0.5458822250366211, "eval_runtime": 10.9739, "eval_samples_per_second": 89.941, "eval_steps_per_second": 45.016, "step": 1500}, {"epoch": 1.3603603603603602, "grad_norm": 0.4498513638973236, "learning_rate": 0.00010953383458646618, "loss": 0.5573, "step": 1510}, {"epoch": 1.3693693693693694, "grad_norm": 0.6221558451652527, "learning_rate": 0.00010893233082706767, "loss": 0.5437, "step": 1520}, {"epoch": 1.3783783783783785, "grad_norm": 0.6130785942077637, "learning_rate": 0.00010833082706766917, "loss": 0.5409, "step": 1530}, {"epoch": 1.3873873873873874, "grad_norm": 0.3421856760978699, "learning_rate": 0.00010772932330827068, "loss": 0.5665, "step": 1540}, {"epoch": 1.3963963963963963, "grad_norm": 0.6165608763694763, "learning_rate": 0.00010712781954887219, "loss": 0.56, "step": 1550}, {"epoch": 1.4054054054054055, "grad_norm": 0.4858126938343048, "learning_rate": 0.0001065263157894737, "loss": 0.5313, "step": 1560}, {"epoch": 1.4144144144144144, "grad_norm": 0.42842501401901245, "learning_rate": 0.00010592481203007518, "loss": 0.5429, "step": 1570}, {"epoch": 1.4234234234234235, "grad_norm": 0.5437096357345581, "learning_rate": 0.0001053233082706767, "loss": 0.5327, "step": 1580}, {"epoch": 1.4324324324324325, "grad_norm": 0.49646615982055664, "learning_rate": 0.0001047218045112782, "loss": 0.5528, "step": 1590}, {"epoch": 1.4414414414414414, "grad_norm": 0.6178253293037415, "learning_rate": 0.00010412030075187971, "loss": 0.5231, "step": 1600}, {"epoch": 1.4504504504504505, "grad_norm": 0.39012956619262695, "learning_rate": 0.00010351879699248121, "loss": 0.5505, "step": 1610}, {"epoch": 1.4594594594594594, "grad_norm": 0.5647482872009277, "learning_rate": 0.0001029172932330827, "loss": 0.5453, "step": 1620}, {"epoch": 1.4684684684684686, "grad_norm": 0.6259228587150574, "learning_rate": 0.0001023157894736842, "loss": 0.5613, "step": 1630}, {"epoch": 1.4774774774774775, "grad_norm": 0.515529453754425, "learning_rate": 0.00010171428571428572, "loss": 0.5283, "step": 1640}, {"epoch": 1.4864864864864864, "grad_norm": 0.6972259283065796, "learning_rate": 0.00010111278195488722, "loss": 0.5483, "step": 1650}, {"epoch": 1.4954954954954955, "grad_norm": 0.49783262610435486, "learning_rate": 0.00010051127819548874, "loss": 0.5439, "step": 1660}, {"epoch": 1.5045045045045045, "grad_norm": 0.5738269090652466, "learning_rate": 9.990977443609023e-05, "loss": 0.5145, "step": 1670}, {"epoch": 1.5135135135135136, "grad_norm": 0.49073758721351624, "learning_rate": 9.930827067669175e-05, "loss": 0.5279, "step": 1680}, {"epoch": 1.5225225225225225, "grad_norm": 0.46948006749153137, "learning_rate": 9.870676691729323e-05, "loss": 0.5949, "step": 1690}, {"epoch": 1.5315315315315314, "grad_norm": 0.46782052516937256, "learning_rate": 9.810526315789475e-05, "loss": 0.5149, "step": 1700}, {"epoch": 1.5405405405405406, "grad_norm": 0.4625255763530731, "learning_rate": 9.750375939849624e-05, "loss": 0.5398, "step": 1710}, {"epoch": 1.5495495495495497, "grad_norm": 0.426644504070282, "learning_rate": 9.690225563909776e-05, "loss": 0.561, "step": 1720}, {"epoch": 1.5585585585585586, "grad_norm": 0.7763584852218628, "learning_rate": 9.630075187969926e-05, "loss": 0.5614, "step": 1730}, {"epoch": 1.5675675675675675, "grad_norm": 0.4463924169540405, "learning_rate": 9.569924812030076e-05, "loss": 0.5084, "step": 1740}, {"epoch": 1.5765765765765765, "grad_norm": 0.42044612765312195, "learning_rate": 9.509774436090226e-05, "loss": 0.5476, "step": 1750}, {"epoch": 1.5855855855855856, "grad_norm": 0.4876978397369385, "learning_rate": 9.449624060150377e-05, "loss": 0.5259, "step": 1760}, {"epoch": 1.5945945945945947, "grad_norm": 0.3924099802970886, "learning_rate": 9.389473684210527e-05, "loss": 0.532, "step": 1770}, {"epoch": 1.6036036036036037, "grad_norm": 0.6168580651283264, "learning_rate": 9.329323308270677e-05, "loss": 0.5341, "step": 1780}, {"epoch": 1.6126126126126126, "grad_norm": 0.4181458652019501, "learning_rate": 9.269172932330827e-05, "loss": 0.5482, "step": 1790}, {"epoch": 1.6216216216216215, "grad_norm": 0.43135395646095276, "learning_rate": 9.209022556390979e-05, "loss": 0.5095, "step": 1800}, {"epoch": 1.6306306306306306, "grad_norm": 0.3625522255897522, "learning_rate": 9.148872180451128e-05, "loss": 0.5479, "step": 1810}, {"epoch": 1.6396396396396398, "grad_norm": 0.5055066347122192, "learning_rate": 9.08872180451128e-05, "loss": 0.514, "step": 1820}, {"epoch": 1.6486486486486487, "grad_norm": 0.6143858432769775, "learning_rate": 9.028571428571428e-05, "loss": 0.5294, "step": 1830}, {"epoch": 1.6576576576576576, "grad_norm": 0.5437126755714417, "learning_rate": 8.96842105263158e-05, "loss": 0.4904, "step": 1840}, {"epoch": 1.6666666666666665, "grad_norm": 0.44989046454429626, "learning_rate": 8.90827067669173e-05, "loss": 0.5548, "step": 1850}, {"epoch": 1.6756756756756757, "grad_norm": 0.4484575092792511, "learning_rate": 8.84812030075188e-05, "loss": 0.5519, "step": 1860}, {"epoch": 1.6846846846846848, "grad_norm": 0.41372039914131165, "learning_rate": 8.78796992481203e-05, "loss": 0.5543, "step": 1870}, {"epoch": 1.6936936936936937, "grad_norm": 0.46148964762687683, "learning_rate": 8.727819548872181e-05, "loss": 0.5583, "step": 1880}, {"epoch": 1.7027027027027026, "grad_norm": 0.45563381910324097, "learning_rate": 8.667669172932331e-05, "loss": 0.5258, "step": 1890}, {"epoch": 1.7117117117117115, "grad_norm": 0.5234844088554382, "learning_rate": 8.607518796992481e-05, "loss": 0.5209, "step": 1900}, {"epoch": 1.7207207207207207, "grad_norm": 0.5044954419136047, "learning_rate": 8.547368421052632e-05, "loss": 0.5191, "step": 1910}, {"epoch": 1.7297297297297298, "grad_norm": 0.5438096523284912, "learning_rate": 8.487218045112782e-05, "loss": 0.5352, "step": 1920}, {"epoch": 1.7387387387387387, "grad_norm": 0.5046774744987488, "learning_rate": 8.427067669172932e-05, "loss": 0.5192, "step": 1930}, {"epoch": 1.7477477477477477, "grad_norm": 0.4054577946662903, "learning_rate": 8.366917293233082e-05, "loss": 0.5177, "step": 1940}, {"epoch": 1.7567567567567568, "grad_norm": 0.4968884289264679, "learning_rate": 8.306766917293234e-05, "loss": 0.5105, "step": 1950}, {"epoch": 1.7657657657657657, "grad_norm": 0.5522557497024536, "learning_rate": 8.246616541353384e-05, "loss": 0.5439, "step": 1960}, {"epoch": 1.7747747747747749, "grad_norm": 0.3693736791610718, "learning_rate": 8.186466165413534e-05, "loss": 0.5229, "step": 1970}, {"epoch": 1.7837837837837838, "grad_norm": 0.4382229149341583, "learning_rate": 8.126315789473685e-05, "loss": 0.5049, "step": 1980}, {"epoch": 1.7927927927927927, "grad_norm": 0.5988510251045227, "learning_rate": 8.066165413533835e-05, "loss": 0.5361, "step": 1990}, {"epoch": 1.8018018018018018, "grad_norm": 0.599049985408783, "learning_rate": 8.006015037593985e-05, "loss": 0.5569, "step": 2000}, {"epoch": 1.8018018018018018, "eval_loss": 0.5323112607002258, "eval_runtime": 11.0192, "eval_samples_per_second": 89.571, "eval_steps_per_second": 44.831, "step": 2000}, {"epoch": 1.810810810810811, "grad_norm": 0.48270484805107117, "learning_rate": 7.945864661654135e-05, "loss": 0.5157, "step": 2010}, {"epoch": 1.8198198198198199, "grad_norm": 0.6730248332023621, "learning_rate": 7.885714285714286e-05, "loss": 0.5201, "step": 2020}, {"epoch": 1.8288288288288288, "grad_norm": 0.5743964910507202, "learning_rate": 7.825563909774436e-05, "loss": 0.5218, "step": 2030}, {"epoch": 1.8378378378378377, "grad_norm": 0.7064577341079712, "learning_rate": 7.765413533834586e-05, "loss": 0.5368, "step": 2040}, {"epoch": 1.8468468468468469, "grad_norm": 0.8078510165214539, "learning_rate": 7.705263157894738e-05, "loss": 0.5545, "step": 2050}, {"epoch": 1.855855855855856, "grad_norm": 0.3951795697212219, "learning_rate": 7.645112781954887e-05, "loss": 0.5011, "step": 2060}, {"epoch": 1.864864864864865, "grad_norm": 0.45073598623275757, "learning_rate": 7.584962406015038e-05, "loss": 0.512, "step": 2070}, {"epoch": 1.8738738738738738, "grad_norm": 0.5479057431221008, "learning_rate": 7.524812030075187e-05, "loss": 0.5174, "step": 2080}, {"epoch": 1.8828828828828827, "grad_norm": 0.43845313787460327, "learning_rate": 7.464661654135339e-05, "loss": 0.5166, "step": 2090}, {"epoch": 1.8918918918918919, "grad_norm": 0.47344285249710083, "learning_rate": 7.404511278195489e-05, "loss": 0.5196, "step": 2100}, {"epoch": 1.900900900900901, "grad_norm": 0.6870599389076233, "learning_rate": 7.344360902255639e-05, "loss": 0.5402, "step": 2110}, {"epoch": 1.90990990990991, "grad_norm": 0.5100296139717102, "learning_rate": 7.28421052631579e-05, "loss": 0.5437, "step": 2120}, {"epoch": 1.9189189189189189, "grad_norm": 0.40259891748428345, "learning_rate": 7.22406015037594e-05, "loss": 0.497, "step": 2130}, {"epoch": 1.9279279279279278, "grad_norm": 0.40956786274909973, "learning_rate": 7.16390977443609e-05, "loss": 0.5366, "step": 2140}, {"epoch": 1.936936936936937, "grad_norm": 0.4792650043964386, "learning_rate": 7.103759398496242e-05, "loss": 0.5403, "step": 2150}, {"epoch": 1.945945945945946, "grad_norm": 0.5469294786453247, "learning_rate": 7.04360902255639e-05, "loss": 0.539, "step": 2160}, {"epoch": 1.954954954954955, "grad_norm": 0.47595807909965515, "learning_rate": 6.983458646616542e-05, "loss": 0.5188, "step": 2170}, {"epoch": 1.9639639639639639, "grad_norm": 0.3790276050567627, "learning_rate": 6.923308270676691e-05, "loss": 0.5262, "step": 2180}, {"epoch": 1.972972972972973, "grad_norm": 0.551114022731781, "learning_rate": 6.863157894736843e-05, "loss": 0.5431, "step": 2190}, {"epoch": 1.981981981981982, "grad_norm": 0.4280155301094055, "learning_rate": 6.803007518796993e-05, "loss": 0.5176, "step": 2200}, {"epoch": 1.990990990990991, "grad_norm": 0.45611968636512756, "learning_rate": 6.742857142857143e-05, "loss": 0.5178, "step": 2210}, {"epoch": 2.0, "grad_norm": 0.5370644330978394, "learning_rate": 6.682706766917293e-05, "loss": 0.5256, "step": 2220}, {"epoch": 2.009009009009009, "grad_norm": 0.4245913624763489, "learning_rate": 6.622556390977444e-05, "loss": 0.512, "step": 2230}, {"epoch": 2.018018018018018, "grad_norm": 0.7245950102806091, "learning_rate": 6.562406015037594e-05, "loss": 0.53, "step": 2240}, {"epoch": 2.027027027027027, "grad_norm": 0.8307613730430603, "learning_rate": 6.502255639097746e-05, "loss": 0.5099, "step": 2250}, {"epoch": 2.036036036036036, "grad_norm": 0.49884846806526184, "learning_rate": 6.442105263157894e-05, "loss": 0.5033, "step": 2260}, {"epoch": 2.045045045045045, "grad_norm": 0.5446817874908447, "learning_rate": 6.381954887218046e-05, "loss": 0.4984, "step": 2270}, {"epoch": 2.054054054054054, "grad_norm": 0.5399331450462341, "learning_rate": 6.321804511278195e-05, "loss": 0.4994, "step": 2280}, {"epoch": 2.063063063063063, "grad_norm": 0.45087647438049316, "learning_rate": 6.261654135338347e-05, "loss": 0.5025, "step": 2290}, {"epoch": 2.0720720720720722, "grad_norm": 0.5728567242622375, "learning_rate": 6.201503759398497e-05, "loss": 0.5017, "step": 2300}, {"epoch": 2.081081081081081, "grad_norm": 0.4927366375923157, "learning_rate": 6.141353383458647e-05, "loss": 0.4995, "step": 2310}, {"epoch": 2.09009009009009, "grad_norm": 0.4540591239929199, "learning_rate": 6.081203007518797e-05, "loss": 0.5136, "step": 2320}, {"epoch": 2.099099099099099, "grad_norm": 0.6258929371833801, "learning_rate": 6.0210526315789475e-05, "loss": 0.5131, "step": 2330}, {"epoch": 2.108108108108108, "grad_norm": 0.5806734561920166, "learning_rate": 5.9609022556390984e-05, "loss": 0.5103, "step": 2340}, {"epoch": 2.1171171171171173, "grad_norm": 0.5310964584350586, "learning_rate": 5.900751879699249e-05, "loss": 0.4794, "step": 2350}, {"epoch": 2.126126126126126, "grad_norm": 0.7135425806045532, "learning_rate": 5.840601503759399e-05, "loss": 0.4875, "step": 2360}, {"epoch": 2.135135135135135, "grad_norm": 0.5332040190696716, "learning_rate": 5.780451127819549e-05, "loss": 0.51, "step": 2370}, {"epoch": 2.144144144144144, "grad_norm": 0.49141520261764526, "learning_rate": 5.7203007518796994e-05, "loss": 0.5063, "step": 2380}, {"epoch": 2.153153153153153, "grad_norm": 0.5271660089492798, "learning_rate": 5.66015037593985e-05, "loss": 0.4996, "step": 2390}, {"epoch": 2.1621621621621623, "grad_norm": 0.5377680063247681, "learning_rate": 5.6000000000000006e-05, "loss": 0.5222, "step": 2400}, {"epoch": 2.171171171171171, "grad_norm": 0.50773024559021, "learning_rate": 5.539849624060151e-05, "loss": 0.4979, "step": 2410}, {"epoch": 2.18018018018018, "grad_norm": 0.5582607388496399, "learning_rate": 5.479699248120301e-05, "loss": 0.5013, "step": 2420}, {"epoch": 2.189189189189189, "grad_norm": 0.45420941710472107, "learning_rate": 5.4195488721804513e-05, "loss": 0.5044, "step": 2430}, {"epoch": 2.1981981981981984, "grad_norm": 0.4775203764438629, "learning_rate": 5.3593984962406016e-05, "loss": 0.476, "step": 2440}, {"epoch": 2.2072072072072073, "grad_norm": 0.4473700225353241, "learning_rate": 5.2992481203007525e-05, "loss": 0.4823, "step": 2450}, {"epoch": 2.2162162162162162, "grad_norm": 0.4927680790424347, "learning_rate": 5.239097744360902e-05, "loss": 0.5007, "step": 2460}, {"epoch": 2.225225225225225, "grad_norm": 0.44295358657836914, "learning_rate": 5.178947368421053e-05, "loss": 0.5016, "step": 2470}, {"epoch": 2.234234234234234, "grad_norm": 0.49784958362579346, "learning_rate": 5.118796992481203e-05, "loss": 0.5092, "step": 2480}, {"epoch": 2.2432432432432434, "grad_norm": 0.525637149810791, "learning_rate": 5.0586466165413535e-05, "loss": 0.4821, "step": 2490}, {"epoch": 2.2522522522522523, "grad_norm": 0.528474748134613, "learning_rate": 4.998496240601504e-05, "loss": 0.484, "step": 2500}, {"epoch": 2.2522522522522523, "eval_loss": 0.5195298194885254, "eval_runtime": 11.0704, "eval_samples_per_second": 89.156, "eval_steps_per_second": 44.623, "step": 2500}, {"epoch": 2.2612612612612613, "grad_norm": 0.5153716802597046, "learning_rate": 4.938345864661654e-05, "loss": 0.4955, "step": 2510}, {"epoch": 2.27027027027027, "grad_norm": 0.6736316084861755, "learning_rate": 4.878195488721804e-05, "loss": 0.5011, "step": 2520}, {"epoch": 2.279279279279279, "grad_norm": 0.5423337817192078, "learning_rate": 4.818045112781955e-05, "loss": 0.4998, "step": 2530}, {"epoch": 2.2882882882882885, "grad_norm": 0.6771421432495117, "learning_rate": 4.7578947368421054e-05, "loss": 0.4986, "step": 2540}, {"epoch": 2.2972972972972974, "grad_norm": 0.5733928680419922, "learning_rate": 4.697744360902256e-05, "loss": 0.5117, "step": 2550}, {"epoch": 2.3063063063063063, "grad_norm": 0.527045726776123, "learning_rate": 4.637593984962406e-05, "loss": 0.5011, "step": 2560}, {"epoch": 2.315315315315315, "grad_norm": 0.45598936080932617, "learning_rate": 4.577443609022556e-05, "loss": 0.4982, "step": 2570}, {"epoch": 2.3243243243243246, "grad_norm": 0.5203675031661987, "learning_rate": 4.517293233082707e-05, "loss": 0.4714, "step": 2580}, {"epoch": 2.3333333333333335, "grad_norm": 0.6360674500465393, "learning_rate": 4.4571428571428574e-05, "loss": 0.4859, "step": 2590}, {"epoch": 2.3423423423423424, "grad_norm": 0.5911279320716858, "learning_rate": 4.3969924812030076e-05, "loss": 0.4907, "step": 2600}, {"epoch": 2.3513513513513513, "grad_norm": 0.5322436690330505, "learning_rate": 4.336842105263158e-05, "loss": 0.5057, "step": 2610}, {"epoch": 2.3603603603603602, "grad_norm": 0.5262420177459717, "learning_rate": 4.276691729323308e-05, "loss": 0.496, "step": 2620}, {"epoch": 2.3693693693693696, "grad_norm": 0.45358899235725403, "learning_rate": 4.216541353383459e-05, "loss": 0.4718, "step": 2630}, {"epoch": 2.3783783783783785, "grad_norm": 0.6625440120697021, "learning_rate": 4.156390977443609e-05, "loss": 0.5068, "step": 2640}, {"epoch": 2.3873873873873874, "grad_norm": 0.5420178174972534, "learning_rate": 4.0962406015037595e-05, "loss": 0.5116, "step": 2650}, {"epoch": 2.3963963963963963, "grad_norm": 0.6283223628997803, "learning_rate": 4.03609022556391e-05, "loss": 0.519, "step": 2660}, {"epoch": 2.4054054054054053, "grad_norm": 0.3648631274700165, "learning_rate": 3.97593984962406e-05, "loss": 0.4576, "step": 2670}, {"epoch": 2.4144144144144146, "grad_norm": 0.6760748028755188, "learning_rate": 3.91578947368421e-05, "loss": 0.5096, "step": 2680}, {"epoch": 2.4234234234234235, "grad_norm": 0.6486302018165588, "learning_rate": 3.855639097744361e-05, "loss": 0.4821, "step": 2690}, {"epoch": 2.4324324324324325, "grad_norm": 0.6686360239982605, "learning_rate": 3.7954887218045114e-05, "loss": 0.5121, "step": 2700}, {"epoch": 2.4414414414414414, "grad_norm": 0.6731064915657043, "learning_rate": 3.735338345864662e-05, "loss": 0.5063, "step": 2710}, {"epoch": 2.4504504504504503, "grad_norm": 0.5825099349021912, "learning_rate": 3.675187969924812e-05, "loss": 0.4896, "step": 2720}, {"epoch": 2.4594594594594597, "grad_norm": 0.4060802161693573, "learning_rate": 3.615037593984962e-05, "loss": 0.5022, "step": 2730}, {"epoch": 2.4684684684684686, "grad_norm": 0.5665688514709473, "learning_rate": 3.554887218045113e-05, "loss": 0.4912, "step": 2740}, {"epoch": 2.4774774774774775, "grad_norm": 0.7656975388526917, "learning_rate": 3.4947368421052634e-05, "loss": 0.4867, "step": 2750}, {"epoch": 2.4864864864864864, "grad_norm": 0.5549020171165466, "learning_rate": 3.4345864661654136e-05, "loss": 0.4995, "step": 2760}, {"epoch": 2.4954954954954953, "grad_norm": 0.6623627543449402, "learning_rate": 3.374436090225564e-05, "loss": 0.5086, "step": 2770}, {"epoch": 2.5045045045045047, "grad_norm": 0.5504870414733887, "learning_rate": 3.314285714285714e-05, "loss": 0.4797, "step": 2780}, {"epoch": 2.5135135135135136, "grad_norm": 0.6800397634506226, "learning_rate": 3.254135338345865e-05, "loss": 0.4928, "step": 2790}, {"epoch": 2.5225225225225225, "grad_norm": 0.5857701301574707, "learning_rate": 3.193984962406015e-05, "loss": 0.4853, "step": 2800}, {"epoch": 2.5315315315315314, "grad_norm": 0.41715872287750244, "learning_rate": 3.1338345864661655e-05, "loss": 0.4676, "step": 2810}, {"epoch": 2.5405405405405403, "grad_norm": 0.4550268352031708, "learning_rate": 3.073684210526316e-05, "loss": 0.4804, "step": 2820}, {"epoch": 2.5495495495495497, "grad_norm": 0.6012568473815918, "learning_rate": 3.013533834586466e-05, "loss": 0.5053, "step": 2830}, {"epoch": 2.5585585585585586, "grad_norm": 0.5324165225028992, "learning_rate": 2.953383458646617e-05, "loss": 0.4964, "step": 2840}, {"epoch": 2.5675675675675675, "grad_norm": 0.7551910281181335, "learning_rate": 2.8932330827067672e-05, "loss": 0.4843, "step": 2850}, {"epoch": 2.5765765765765765, "grad_norm": 0.6331012845039368, "learning_rate": 2.8330827067669175e-05, "loss": 0.4825, "step": 2860}, {"epoch": 2.5855855855855854, "grad_norm": 0.5248553156852722, "learning_rate": 2.7729323308270677e-05, "loss": 0.5006, "step": 2870}, {"epoch": 2.5945945945945947, "grad_norm": 0.7514132857322693, "learning_rate": 2.712781954887218e-05, "loss": 0.4811, "step": 2880}, {"epoch": 2.6036036036036037, "grad_norm": 0.5169869065284729, "learning_rate": 2.652631578947369e-05, "loss": 0.4961, "step": 2890}, {"epoch": 2.6126126126126126, "grad_norm": 0.4700373709201813, "learning_rate": 2.592481203007519e-05, "loss": 0.4797, "step": 2900}, {"epoch": 2.6216216216216215, "grad_norm": 0.625378429889679, "learning_rate": 2.5323308270676694e-05, "loss": 0.4818, "step": 2910}, {"epoch": 2.6306306306306304, "grad_norm": 0.49508848786354065, "learning_rate": 2.4721804511278196e-05, "loss": 0.4876, "step": 2920}, {"epoch": 2.6396396396396398, "grad_norm": 0.5408915877342224, "learning_rate": 2.4120300751879702e-05, "loss": 0.4968, "step": 2930}, {"epoch": 2.6486486486486487, "grad_norm": 0.5150611400604248, "learning_rate": 2.3518796992481205e-05, "loss": 0.4929, "step": 2940}, {"epoch": 2.6576576576576576, "grad_norm": 0.5236762166023254, "learning_rate": 2.2917293233082707e-05, "loss": 0.5089, "step": 2950}, {"epoch": 2.6666666666666665, "grad_norm": 0.5596017837524414, "learning_rate": 2.2315789473684213e-05, "loss": 0.5005, "step": 2960}, {"epoch": 2.6756756756756754, "grad_norm": 0.5051065683364868, "learning_rate": 2.1714285714285715e-05, "loss": 0.486, "step": 2970}, {"epoch": 2.684684684684685, "grad_norm": 0.4620160460472107, "learning_rate": 2.111278195488722e-05, "loss": 0.5058, "step": 2980}, {"epoch": 2.6936936936936937, "grad_norm": 0.5537675023078918, "learning_rate": 2.0511278195488724e-05, "loss": 0.469, "step": 2990}, {"epoch": 2.7027027027027026, "grad_norm": 0.6279346346855164, "learning_rate": 1.9909774436090226e-05, "loss": 0.4943, "step": 3000}, {"epoch": 2.7027027027027026, "eval_loss": 0.5090791583061218, "eval_runtime": 11.0963, "eval_samples_per_second": 88.949, "eval_steps_per_second": 44.519, "step": 3000}, {"epoch": 2.7117117117117115, "grad_norm": 0.6809214353561401, "learning_rate": 1.9308270676691732e-05, "loss": 0.4947, "step": 3010}, {"epoch": 2.7207207207207205, "grad_norm": 0.6410564184188843, "learning_rate": 1.8706766917293235e-05, "loss": 0.4932, "step": 3020}, {"epoch": 2.72972972972973, "grad_norm": 0.5777915716171265, "learning_rate": 1.810526315789474e-05, "loss": 0.507, "step": 3030}, {"epoch": 2.7387387387387387, "grad_norm": 0.5298306941986084, "learning_rate": 1.7503759398496243e-05, "loss": 0.4872, "step": 3040}, {"epoch": 2.7477477477477477, "grad_norm": 1.4430773258209229, "learning_rate": 1.6902255639097746e-05, "loss": 0.4958, "step": 3050}, {"epoch": 2.756756756756757, "grad_norm": 0.5641828775405884, "learning_rate": 1.630075187969925e-05, "loss": 0.5022, "step": 3060}, {"epoch": 2.7657657657657655, "grad_norm": 0.580622136592865, "learning_rate": 1.5699248120300754e-05, "loss": 0.4964, "step": 3070}, {"epoch": 2.774774774774775, "grad_norm": 0.5859368443489075, "learning_rate": 1.5097744360902255e-05, "loss": 0.4785, "step": 3080}, {"epoch": 2.7837837837837838, "grad_norm": 0.5920839309692383, "learning_rate": 1.449624060150376e-05, "loss": 0.4876, "step": 3090}, {"epoch": 2.7927927927927927, "grad_norm": 0.4969504773616791, "learning_rate": 1.3894736842105263e-05, "loss": 0.4718, "step": 3100}, {"epoch": 2.801801801801802, "grad_norm": 0.925478458404541, "learning_rate": 1.3293233082706769e-05, "loss": 0.503, "step": 3110}, {"epoch": 2.810810810810811, "grad_norm": 0.5382892489433289, "learning_rate": 1.2691729323308271e-05, "loss": 0.4847, "step": 3120}, {"epoch": 2.81981981981982, "grad_norm": 0.5396475195884705, "learning_rate": 1.2090225563909776e-05, "loss": 0.4889, "step": 3130}, {"epoch": 2.828828828828829, "grad_norm": 0.6806105375289917, "learning_rate": 1.148872180451128e-05, "loss": 0.4913, "step": 3140}, {"epoch": 2.8378378378378377, "grad_norm": 0.5747053623199463, "learning_rate": 1.0887218045112782e-05, "loss": 0.4909, "step": 3150}, {"epoch": 2.846846846846847, "grad_norm": 0.5864428281784058, "learning_rate": 1.0285714285714286e-05, "loss": 0.4964, "step": 3160}, {"epoch": 2.855855855855856, "grad_norm": 0.5565223097801208, "learning_rate": 9.68421052631579e-06, "loss": 0.4647, "step": 3170}, {"epoch": 2.864864864864865, "grad_norm": 0.5396762490272522, "learning_rate": 9.082706766917295e-06, "loss": 0.4775, "step": 3180}, {"epoch": 2.873873873873874, "grad_norm": 0.6331630945205688, "learning_rate": 8.481203007518797e-06, "loss": 0.4753, "step": 3190}, {"epoch": 2.8828828828828827, "grad_norm": 0.5051870942115784, "learning_rate": 7.879699248120301e-06, "loss": 0.4801, "step": 3200}, {"epoch": 2.891891891891892, "grad_norm": 0.5873674154281616, "learning_rate": 7.278195488721805e-06, "loss": 0.5055, "step": 3210}, {"epoch": 2.900900900900901, "grad_norm": 0.5961098074913025, "learning_rate": 6.676691729323309e-06, "loss": 0.5042, "step": 3220}, {"epoch": 2.90990990990991, "grad_norm": 0.5582064390182495, "learning_rate": 6.075187969924812e-06, "loss": 0.4878, "step": 3230}, {"epoch": 2.918918918918919, "grad_norm": 0.4833930432796478, "learning_rate": 5.4736842105263165e-06, "loss": 0.502, "step": 3240}, {"epoch": 2.9279279279279278, "grad_norm": 0.6477227807044983, "learning_rate": 4.87218045112782e-06, "loss": 0.4949, "step": 3250}, {"epoch": 2.936936936936937, "grad_norm": 0.6848986148834229, "learning_rate": 4.270676691729324e-06, "loss": 0.4954, "step": 3260}, {"epoch": 2.945945945945946, "grad_norm": 0.6179794669151306, "learning_rate": 3.669172932330827e-06, "loss": 0.4757, "step": 3270}, {"epoch": 2.954954954954955, "grad_norm": 0.5661334991455078, "learning_rate": 3.067669172932331e-06, "loss": 0.4654, "step": 3280}, {"epoch": 2.963963963963964, "grad_norm": 0.5677876472473145, "learning_rate": 2.466165413533835e-06, "loss": 0.4809, "step": 3290}, {"epoch": 2.972972972972973, "grad_norm": 0.5115178823471069, "learning_rate": 1.8646616541353384e-06, "loss": 0.4759, "step": 3300}, {"epoch": 2.981981981981982, "grad_norm": 0.5465250611305237, "learning_rate": 1.2631578947368422e-06, "loss": 0.468, "step": 3310}, {"epoch": 2.990990990990991, "grad_norm": 0.6913829445838928, "learning_rate": 6.61654135338346e-07, "loss": 0.4721, "step": 3320}, {"epoch": 3.0, "grad_norm": 0.5633916854858398, "learning_rate": 6.015037593984963e-08, "loss": 0.4986, "step": 3330}], "logging_steps": 10, "max_steps": 3330, "num_input_tokens_seen": 0, "num_train_epochs": 3, "save_steps": 500, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": true}, "attributes": {}}}, "total_flos": 1.6136584839168e+16, "train_batch_size": 2, "trial_name": null, "trial_params": null}