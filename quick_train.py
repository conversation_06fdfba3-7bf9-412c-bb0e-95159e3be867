#!/usr/bin/env python3
"""
Quick training script with reduced parameters for testing
"""

from thunderbird_log_classification import Config, main
import sys

# Override config for quick training
class QuickConfig(Config):
    # Reduced sample size for quick testing
    SAMPLE_SIZE = 1000
    
    # Smaller model for faster training
    MODEL_NAME = "unsloth/llama-3-8b-bnb-4bit"  # Keep the same for consistency
    MAX_SEQ_LENGTH = 256  # Reduced sequence length
    
    # Reduced training parameters
    NUM_TRAIN_EPOCHS = 1
    PER_DEVICE_TRAIN_BATCH_SIZE = 1
    GRADIENT_ACCUMULATION_STEPS = 2
    WARMUP_STEPS = 2
    LOGGING_STEPS = 5
    SAVE_STEPS = 50
    EVAL_STEPS = 50
    
    # Quick training output directory
    OUTPUT_DIR = "./thunderbird_log_classifier_quick"

def quick_train():
    """Run quick training with reduced parameters"""
    print("Quick Training Mode - Reduced Parameters for Testing")
    print("=" * 60)
    print(f"Sample size: {QuickConfig.SAMPLE_SIZE}")
    print(f"Epochs: {QuickConfig.NUM_TRAIN_EPOCHS}")
    print(f"Max sequence length: {QuickConfig.MAX_SEQ_LENGTH}")
    print(f"Output directory: {QuickConfig.OUTPUT_DIR}")
    print("=" * 60)
    
    # Replace the global Config with QuickConfig
    import thunderbird_log_classification
    thunderbird_log_classification.Config = QuickConfig
    
    # Run training
    main()

if __name__ == "__main__":
    quick_train()
