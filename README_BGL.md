# BGL Log Anomaly Classification with Unsloth

This project implements a fine-tuned language model for classifying log entries from the BGL (BlueGene/L) supercomputer dataset as either normal or anomalous using the Unsloth framework.

## Dataset

The BGL dataset contains logs from a BlueGene/L supercomputer system at Lawrence Livermore National Labs with:
- **Total entries**: 4,747,963 log entries
- **Normal logs**: ~92.7% (entries starting with "-")
- **Anomalous logs**: ~7.3% (entries starting with error codes like APPREAD, etc.)

## Features

- **Efficient Fine-tuning**: Uses Unsloth for fast and memory-efficient training
- **Batch Processing**: Optimized evaluation with 5-15x speedup using GPU parallelization
- **Proper Data Splitting**: Implements train-validation-test split to prevent data leakage
- **Data Leakage Prevention**: Ensures test set is completely held out during training
- **Multiple Modes**: Training, testing, and interactive classification modes
- **LoRA Adaptation**: Uses Low-Rank Adaptation for efficient parameter updates
- **Clean Data Processing**: Removes label markers to ensure genuine content-based learning
- **Comprehensive Evaluation**: Provides accuracy, precision, recall, F1-score, and confusion matrix

## Installation

1. Clone this repository
2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Data Splitting & Leakage Prevention

This implementation uses a **proper train-validation-test split** to prevent data leakage:

1. **Single Split**: All data is split once at the beginning into train (80%), validation (10%), and test (10%) sets
2. **Line-based Sampling**: Randomly samples line numbers first, then reads those specific lines
3. **No Overlap**: Ensures zero overlap between training, validation, and test sets
4. **Held-out Test Set**: Test samples are saved separately and never seen during training
5. **Reproducible**: Uses fixed random seeds for consistent splits

### Why This Matters

The previous approach randomly sampled from the entire dataset for both training and testing, which could lead to:
- **Data Leakage**: Test samples that were already seen during training
- **Artificially High Scores**: Inflated performance metrics due to memorization
- **Poor Generalization**: Models that don't perform well on truly unseen data

## Usage

### Training

Train the model on the BGL dataset:

```bash
python bgl_log_classification.py train
```

This will:
- Sample 10,000 log entries from the dataset
- Split into 90% training, 10% validation
- Fine-tune the model using LoRA
- Save the trained model to `./bgl_log_classifier`

### Testing

Evaluate the trained model:

```bash
python bgl_log_classification.py test [model_path] [num_samples]
```

Example:
```bash
python bgl_log_classification.py test ./bgl_log_classifier 1000
```

### Interactive Testing

Test individual log entries interactively:

```bash
python bgl_log_classification.py interactive [model_path]
```

## Configuration

Key configuration parameters in the `Config` class:

- `SAMPLE_SIZE`: Number of samples for training (default: 10,000)
- `MODEL_NAME`: Base model to fine-tune (default: "unsloth/Llama-3.2-1B-Instruct-bnb-4bit")
- `MAX_SEQ_LENGTH`: Maximum sequence length (default: 512)
- `NUM_TRAIN_EPOCHS`: Training epochs (default: 3)
- `LEARNING_RATE`: Learning rate (default: 2e-4)

## Model Architecture

- **Base Model**: Llama-3.2-1B with 4-bit quantization
- **Fine-tuning**: LoRA (Low-Rank Adaptation) with rank 16
- **Target Modules**: All attention and MLP projection layers
- **Training**: Supervised fine-tuning with classification prompts

## Example Log Classifications

**Normal Log Entry:**
```
Input: "1117838570 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.42.50.363779 R02-M1-N0-C:J12-U11 RAS KERNEL INFO instruction cache parity error corrected"
Output: normal
```

**Anomalous Log Entry:**
```
Input: "1117869872 2005.06.04 R23-M1-N8-I:J18-U11 2005-06-04-00.24.32.398284 R23-M1-N8-I:J18-U11 RAS APP FATAL ciod: failed to read message prefix on control stream"
Output: anomaly
```

## Performance Metrics

The model is evaluated using:
- **Accuracy**: Overall classification accuracy
- **Precision**: Precision for both classes
- **Recall**: Recall for both classes
- **F1-Score**: Weighted F1-score
- **Confusion Matrix**: Detailed classification breakdown

## Hardware Requirements

- **GPU**: NVIDIA GPU with at least 8GB VRAM (recommended)
- **RAM**: At least 16GB system RAM
- **Storage**: ~3GB for model and dataset

## File Structure

```
.
├── bgl_log_classification.py         # Main training script
├── requirements.txt                  # Python dependencies
├── README_BGL.md                    # This file
├── datasets/
│   └── BGL/
│       ├── BGL.log                  # Dataset file
│       ├── BGL_templates.csv        # Log templates
│       └── README.md                # Dataset info
└── bgl_log_classifier/              # Output directory (created after training)
    ├── adapter_config.json
    ├── adapter_model.safetensors
    ├── config.json
    ├── tokenizer.json
    └── evaluation_results.json
```

## Key Differences from Thunderbird

1. **Dataset Size**: BGL has ~4.7M entries vs Thunderbird's 10M
2. **Anomaly Rate**: BGL has ~7.3% anomalies vs Thunderbird's ~3.5%
3. **Log Format**: Similar structure but different error types (APPREAD vs ECC/VAPI)
4. **System Type**: BlueGene/L vs Thunderbird supercomputer

## Troubleshooting

1. **CUDA Out of Memory**: Reduce `PER_DEVICE_TRAIN_BATCH_SIZE` or `MAX_SEQ_LENGTH`
2. **Slow Training**: Ensure you have a compatible GPU and CUDA installed
3. **Import Errors**: Make sure all dependencies are installed correctly

## Citation

If you use this code or the BGL dataset, please cite:

```bibtex
@inproceedings{oliner2007supercomputers,
  title={What supercomputers say: A study of five system logs},
  author={Oliner, Adam J and Stearley, Jon},
  booktitle={37th Annual IEEE/IFIP International Conference on Dependable Systems and Networks (DSN'07)},
  pages={575--584},
  year={2007},
  organization={IEEE}
}
```
