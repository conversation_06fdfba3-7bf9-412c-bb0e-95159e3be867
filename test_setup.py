#!/usr/bin/env python3
"""
Test script to verify the setup and basic functionality
"""

import os
import sys
import torch
from thunderbird_log_classification import (
    Config, parse_log_line, create_prompt, load_and_sample_data
)

def test_dependencies():
    """Test if all required dependencies are installed"""
    print("Testing dependencies...")
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
        
        import transformers
        print(f"✓ Transformers {transformers.__version__}")
        
        import datasets
        print(f"✓ Datasets {datasets.__version__}")
        
        import unsloth
        print("✓ Unsloth")
        
        import sklearn
        print(f"✓ Scikit-learn {sklearn.__version__}")
        
        import pandas
        print(f"✓ Pandas {pandas.__version__}")
        
        import numpy
        print(f"✓ NumPy {numpy.__version__}")
        
        return True
        
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        return False

def test_cuda():
    """Test CUDA availability"""
    print("\nTesting CUDA...")
    
    if torch.cuda.is_available():
        print(f"✓ CUDA available")
        print(f"  Device count: {torch.cuda.device_count()}")
        print(f"  Current device: {torch.cuda.current_device()}")
        print(f"  Device name: {torch.cuda.get_device_name()}")
        print(f"  Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        return True
    else:
        print("⚠ CUDA not available - training will be slower on CPU")
        return False

def test_dataset():
    """Test dataset loading"""
    print("\nTesting dataset...")
    
    if not os.path.exists(Config.DATASET_PATH):
        print(f"✗ Dataset not found at {Config.DATASET_PATH}")
        return False
    
    print(f"✓ Dataset found at {Config.DATASET_PATH}")
    
    # Test file size
    file_size = os.path.getsize(Config.DATASET_PATH) / (1024**3)  # GB
    print(f"  File size: {file_size:.1f} GB")
    
    return True

def test_log_parsing():
    """Test log parsing functionality"""
    print("\nTesting log parsing...")
    
    # Test normal log
    normal_log = "- 1131523501 2005.11.09 aadmin1 Nov 10 00:05:01 src@aadmin1 in.tftpd[14620]: tftp: client does not accept options"
    label, content = parse_log_line(normal_log)
    
    if label == 'normal':
        print("✓ Normal log parsing works")
    else:
        print(f"✗ Normal log parsing failed: got {label}")
        return False
    
    # Test anomaly log
    anomaly_log = "ECC 1131674844 2005.11.10 cn994 Nov 10 18:07:24 cn994/cn994 Server Administrator: Memory device status is critical"
    label, content = parse_log_line(anomaly_log)
    
    if label == 'anomaly':
        print("✓ Anomaly log parsing works")
    else:
        print(f"✗ Anomaly log parsing failed: got {label}")
        return False
    
    return True

def test_prompt_creation():
    """Test prompt creation"""
    print("\nTesting prompt creation...")
    
    content = "Test log entry"
    prompt = create_prompt(content)
    
    if "Test log entry" in prompt and "Classification:" in prompt:
        print("✓ Prompt creation works")
        return True
    else:
        print("✗ Prompt creation failed")
        return False

def test_small_sample():
    """Test loading a small sample of data"""
    print("\nTesting small data sample...")
    
    if not os.path.exists(Config.DATASET_PATH):
        print("⚠ Skipping - dataset not available")
        return True
    
    try:
        # Load a very small sample
        samples = load_and_sample_data(Config.DATASET_PATH, 100)
        
        if len(samples) > 0:
            print(f"✓ Successfully loaded {len(samples)} samples")
            
            # Check distribution
            normal_count = sum(1 for label, _ in samples if label == 'normal')
            anomaly_count = len(samples) - normal_count
            print(f"  Normal: {normal_count}, Anomaly: {anomaly_count}")
            
            return True
        else:
            print("✗ No samples loaded")
            return False
            
    except Exception as e:
        print(f"✗ Error loading samples: {e}")
        return False

def main():
    """Run all tests"""
    print("Thunderbird Log Classification Setup Test")
    print("=" * 50)
    
    tests = [
        test_dependencies,
        test_cuda,
        test_dataset,
        test_log_parsing,
        test_prompt_creation,
        test_small_sample,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! Ready to start training.")
    else:
        print("⚠ Some tests failed. Please check the issues above.")
        
        if not results[0]:  # Dependencies failed
            print("\nTo install dependencies, run:")
            print("pip install -r requirements.txt")
        
        if not results[2]:  # Dataset not found
            print(f"\nPlease ensure the dataset is available at:")
            print(f"{Config.DATASET_PATH}")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
