#!/usr/bin/env python3
"""
Thunderbird Log Anomaly Classification Fine-tuning Script using Unsloth

This script fine-tunes a language model for log anomaly detection using the Thunderbird dataset.
Normal logs start with "-", anomalous logs start with other prefixes (ECC, VAPI, etc.).
"""

import os
import random
import pandas as pd
import numpy as np
from typing import List, Tuple, Dict
import torch
from datasets import Dataset
import unsloth  # Import unsloth first for optimizations
from unsloth import FastLanguageModel
from unsloth import is_bfloat16_supported
from transformers import TrainingArguments
from trl import SFTTrainer
import re
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, confusion_matrix
import json
from datetime import datetime

# Configuration
class Config:
    # Dataset settings
    DATASET_PATH = "datasets/Thunderbird/Thunderbird_10M.log"
    SAMPLE_SIZE = 5000  # Total number of samples to extract
    TRAIN_RATIO = 0.8   # 80% for training
    VAL_RATIO = 0.1     # 10% for validation
    TEST_RATIO = 0.1    # 10% for testing (held-out)
    
    # Model settings
    MODEL_NAME = "unsloth/Llama-3.2-1B-Instruct-bnb-4bit"  # You can change this to other models
    MAX_SEQ_LENGTH = 512
    LOAD_IN_4BIT = True
    
    # LoRA settings
    LORA_R = 16
    LORA_ALPHA = 16
    LORA_DROPOUT = 0
    BIAS = "none"
    USE_GRADIENT_CHECKPOINTING = "unsloth"
    
    # Training settings
    OUTPUT_DIR = "./llama-3.2-1B-Instruct-bnb-4bit"
    NUM_TRAIN_EPOCHS = 3
    PER_DEVICE_TRAIN_BATCH_SIZE = 2
    PER_DEVICE_EVAL_BATCH_SIZE = 2
    GRADIENT_ACCUMULATION_STEPS = 4
    WARMUP_STEPS = 5
    LEARNING_RATE = 2e-4
    LOGGING_STEPS = 10
    SAVE_STEPS = 500
    EVAL_STEPS = 500
    
    # Random seed for reproducibility
    RANDOM_SEED = 42

    # Evaluation settings
    EVAL_BATCH_SIZE = 8   # Conservative batch size to avoid Triton/CUDA issues
    FALLBACK_BATCH_SIZE = 1  # Fallback to individual processing if batch fails

def set_random_seed(seed: int):
    """Set random seed for reproducibility"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(seed)

def parse_log_line(line: str) -> Tuple[str, str]:
    """
    Parse a log line and extract the label and content

    Args:
        line: Raw log line

    Returns:
        Tuple of (label, clean_content) where label is 'normal' or 'anomaly'
        and clean_content has all label markers removed
    """
    line = line.strip()
    if line.startswith('-'):
        label = 'normal'
        # Remove the leading "- " and extract the actual log content
        content = line[2:] if len(line) > 2 else line
    else:
        label = 'anomaly'
        # For anomaly logs, remove the prefix (ECC, VAPI, etc.) to get clean content
        # Split on first space and take everything after the first token
        parts = line.split(' ', 1)
        content = parts[1] if len(parts) > 1 else line

    return label, content

def create_train_val_test_split(dataset_path: str, total_sample_size: int, train_ratio: float = 0.8, val_ratio: float = 0.1, test_ratio: float = 0.1) -> Tuple[List[Tuple[str, str]], List[Tuple[str, str]], List[Tuple[str, str]]]:
    """
    Create a proper train-validation-test split by sampling line numbers first

    Args:
        dataset_path: Path to the log file
        total_sample_size: Total number of samples to extract
        train_ratio: Ratio for training set
        val_ratio: Ratio for validation set
        test_ratio: Ratio for test set

    Returns:
        Tuple of (train_samples, val_samples, test_samples)
    """
    print(f"Creating train-val-test split from {dataset_path}...")

    # First, count total lines
    with open(dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
        total_lines = sum(1 for _ in f)

    print(f"Total lines in dataset: {total_lines}")

    # Sample line numbers randomly
    sampled_line_numbers = sorted(random.sample(range(total_lines), min(total_sample_size, total_lines)))
    print(f"Sampled {len(sampled_line_numbers)} line numbers")

    # Read the sampled lines
    samples = []
    with open(dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
        for line_idx, line in enumerate(f):
            if line_idx in sampled_line_numbers:
                label, content = parse_log_line(line)
                if content.strip():  # Only include non-empty content
                    samples.append((label, content.strip()))

    print(f"Collected {len(samples)} valid samples")

    # Shuffle samples to ensure random distribution
    random.shuffle(samples)

    # Split into train, validation, and test sets
    train_size = int(len(samples) * train_ratio)
    val_size = int(len(samples) * val_ratio)
    # test_size is the remainder to ensure we use all samples

    train_samples = samples[:train_size]
    val_samples = samples[train_size:train_size + val_size]
    test_samples = samples[train_size + val_size:]

    # Print statistics for each split
    for split_name, split_samples in [("Training", train_samples), ("Validation", val_samples), ("Test", test_samples)]:
        normal_count = sum(1 for label, _ in split_samples if label == 'normal')
        anomaly_count = len(split_samples) - normal_count
        print(f"{split_name} set: {len(split_samples)} samples")
        print(f"  Normal: {normal_count} ({normal_count/len(split_samples)*100:.1f}%)")
        print(f"  Anomaly: {anomaly_count} ({anomaly_count/len(split_samples)*100:.1f}%)")

    return train_samples, val_samples, test_samples

def load_and_sample_data(dataset_path: str, sample_size: int) -> List[Tuple[str, str]]:
    """
    DEPRECATED: Use create_train_val_test_split instead to avoid data leakage

    This function is kept for backward compatibility but should not be used
    for training/testing as it can cause data leakage.
    """
    print("WARNING: Using deprecated load_and_sample_data function.")
    print("This may cause data leakage. Use create_train_val_test_split instead.")

    print(f"Loading data from {dataset_path}...")
    
    # First, count total lines to calculate sampling probability
    with open(dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
        total_lines = sum(1 for _ in f)
    
    print(f"Total lines in dataset: {total_lines}")
    
    # Calculate sampling probability
    sampling_prob = min(1.0, sample_size / total_lines)
    print(f"Sampling probability: {sampling_prob:.4f}")
    
    samples = []
    with open(dataset_path, 'r', encoding='utf-8', errors='ignore') as f:
        for line in f:
            if random.random() < sampling_prob:
                label, content = parse_log_line(line)
                if content.strip():  # Only include non-empty content
                    samples.append((label, content.strip()))
                
                if len(samples) >= sample_size:
                    break
    
    print(f"Collected {len(samples)} samples")
    
    # Check distribution
    normal_count = sum(1 for label, _ in samples if label == 'normal')
    anomaly_count = len(samples) - normal_count
    print(f"Normal samples: {normal_count} ({normal_count/len(samples)*100:.1f}%)")
    print(f"Anomaly samples: {anomaly_count} ({anomaly_count/len(samples)*100:.1f}%)")
    
    return samples

def create_prompt(content: str) -> str:
    """
    Create a prompt for the model
    
    Args:
        content: Log content
        
    Returns:
        Formatted prompt
    """
    return f"""Analyze the following log entry and classify it as either 'normal' or 'anomaly':

Log entry: {content}

Classification:"""

def create_training_example(content: str, label: str) -> str:
    """
    Create a complete training example with prompt and response
    
    Args:
        content: Log content
        label: Classification label
        
    Returns:
        Complete training example
    """
    prompt = create_prompt(content)
    return f"{prompt} {label}"

def prepare_dataset(samples: List[Tuple[str, str]], train_ratio: float) -> Tuple[Dataset, Dataset]:
    """
    DEPRECATED: Use prepare_dataset_from_samples instead

    Prepare training and validation datasets

    Args:
        samples: List of (label, content) tuples
        train_ratio: Ratio of data to use for training

    Returns:
        Tuple of (train_dataset, val_dataset)
    """
    print("WARNING: Using deprecated prepare_dataset function.")
    print("Use prepare_dataset_from_samples instead for proper data splitting.")

    print("Preparing datasets...")

    # Shuffle samples
    random.shuffle(samples)

    # Split into train and validation
    split_idx = int(len(samples) * train_ratio)
    train_samples = samples[:split_idx]
    val_samples = samples[split_idx:]

    print(f"Training samples: {len(train_samples)}")
    print(f"Validation samples: {len(val_samples)}")

    # Create training examples
    train_texts = [create_training_example(content, label) for label, content in train_samples]
    val_texts = [create_training_example(content, label) for label, content in val_samples]

    # Create datasets
    train_dataset = Dataset.from_dict({"text": train_texts})
    val_dataset = Dataset.from_dict({"text": val_texts})

    return train_dataset, val_dataset

def prepare_dataset_from_samples(train_samples: List[Tuple[str, str]], val_samples: List[Tuple[str, str]]) -> Tuple[Dataset, Dataset]:
    """
    Prepare training and validation datasets from pre-split samples

    Args:
        train_samples: List of (label, content) tuples for training
        val_samples: List of (label, content) tuples for validation

    Returns:
        Tuple of (train_dataset, val_dataset)
    """
    print("Preparing datasets from pre-split samples...")

    print(f"Training samples: {len(train_samples)}")
    print(f"Validation samples: {len(val_samples)}")

    # Create training examples
    train_texts = [create_training_example(content, label) for label, content in train_samples]
    val_texts = [create_training_example(content, label) for label, content in val_samples]

    # Create datasets
    train_dataset = Dataset.from_dict({"text": train_texts})
    val_dataset = Dataset.from_dict({"text": val_texts})

    return train_dataset, val_dataset

def setup_model_and_tokenizer(config: Config):
    """
    Setup the model and tokenizer using Unsloth
    
    Args:
        config: Configuration object
        
    Returns:
        Tuple of (model, tokenizer)
    """
    print("Setting up model and tokenizer...")
    
    model, tokenizer = FastLanguageModel.from_pretrained(
        model_name=config.MODEL_NAME,
        max_seq_length=config.MAX_SEQ_LENGTH,
        dtype=None,  # Auto-detect
        load_in_4bit=config.LOAD_IN_4BIT,
    )
    
    # Setup LoRA
    model = FastLanguageModel.get_peft_model(
        model,
        r=config.LORA_R,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj",
                       "gate_proj", "up_proj", "down_proj"],
        lora_alpha=config.LORA_ALPHA,
        lora_dropout=config.LORA_DROPOUT,
        bias=config.BIAS,
        use_gradient_checkpointing=config.USE_GRADIENT_CHECKPOINTING,
        random_state=config.RANDOM_SEED,
        use_rslora=False,
        loftq_config=None,
    )
    
    return model, tokenizer

def main():
    """Main training function"""
    print("Starting Thunderbird Log Classification Fine-tuning")
    print("=" * 60)

    # Set random seed
    set_random_seed(Config.RANDOM_SEED)

    # Create proper train-validation-test split
    train_samples, val_samples, test_samples = create_train_val_test_split(
        Config.DATASET_PATH,
        Config.SAMPLE_SIZE,
        train_ratio=Config.TRAIN_RATIO,
        val_ratio=Config.VAL_RATIO,
        test_ratio=Config.TEST_RATIO
    )

    # Save test samples for later evaluation (to avoid data leakage)
    test_samples_path = os.path.join(Config.OUTPUT_DIR, "test_samples.json")
    os.makedirs(Config.OUTPUT_DIR, exist_ok=True)
    with open(test_samples_path, "w") as f:
        json.dump(test_samples, f, indent=2)
    print(f"Test samples saved to {test_samples_path}")

    # Prepare datasets from the split data
    train_dataset, val_dataset = prepare_dataset_from_samples(train_samples, val_samples)
    
    # Setup model and tokenizer
    model, tokenizer = setup_model_and_tokenizer(Config)
    
    print("Model setup complete. Starting training...")
    
    # Training arguments
    training_args = TrainingArguments(
        output_dir=Config.OUTPUT_DIR,
        num_train_epochs=Config.NUM_TRAIN_EPOCHS,
        per_device_train_batch_size=Config.PER_DEVICE_TRAIN_BATCH_SIZE,
        per_device_eval_batch_size=Config.PER_DEVICE_EVAL_BATCH_SIZE,
        gradient_accumulation_steps=Config.GRADIENT_ACCUMULATION_STEPS,
        warmup_steps=Config.WARMUP_STEPS,
        learning_rate=Config.LEARNING_RATE,
        fp16=not is_bfloat16_supported(),
        bf16=is_bfloat16_supported(),
        logging_steps=Config.LOGGING_STEPS,
        eval_strategy="steps",  # Changed from evaluation_strategy
        eval_steps=Config.EVAL_STEPS,
        save_steps=Config.SAVE_STEPS,
        save_total_limit=2,
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        report_to=None,  # Disable wandb/tensorboard
        seed=Config.RANDOM_SEED,
    )
    
    # Create trainer
    trainer = SFTTrainer(
        model=model,
        tokenizer=tokenizer,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        dataset_text_field="text",
        max_seq_length=Config.MAX_SEQ_LENGTH,
        dataset_num_proc=2,
        packing=False,
        args=training_args,
    )
    
    # Start training
    print("Starting training...")
    trainer.train()

    # Save the final model - save only the LoRA adapter
    print("Saving model...")
    model.save_pretrained(Config.OUTPUT_DIR)
    tokenizer.save_pretrained(Config.OUTPUT_DIR)
    
    # Save configuration
    config_dict = {attr: getattr(Config, attr) for attr in dir(Config) 
                   if not attr.startswith('_')}
    with open(os.path.join(Config.OUTPUT_DIR, "config.json"), "w") as f:
        json.dump(config_dict, f, indent=2)
    
    print(f"Training complete! Model saved to {Config.OUTPUT_DIR}")

def load_trained_model(model_path: str):
    """
    Load a trained model for inference

    Args:
        model_path: Path to the saved model

    Returns:
        Tuple of (model, tokenizer)
    """
    print(f"Loading trained model from {model_path}...")

    # First load the base model
    model, tokenizer = FastLanguageModel.from_pretrained(
        model_name=Config.MODEL_NAME,  # Load the original base model
        max_seq_length=Config.MAX_SEQ_LENGTH,
        dtype=None,
        load_in_4bit=Config.LOAD_IN_4BIT,
    )

    # Then load the LoRA adapter
    from peft import PeftModel
    model = PeftModel.from_pretrained(model, model_path)

    # Enable inference mode
    FastLanguageModel.for_inference(model)

    return model, tokenizer

def predict_log_entry(model, tokenizer, log_content: str) -> str:
    """
    Predict the classification of a single log entry

    Args:
        model: Trained model
        tokenizer: Tokenizer
        log_content: Log content to classify

    Returns:
        Predicted classification ('normal' or 'anomaly')
    """
    # Use batch prediction for consistency and efficiency
    predictions = predict_batch(model, tokenizer, [log_content], batch_size=1)
    return predictions[0]

def predict_batch(model, tokenizer, log_contents: List[str], batch_size: int = 8) -> List[str]:
    """
    Predict classifications for a batch of log entries efficiently

    Args:
        model: Trained model
        tokenizer: Tokenizer
        log_contents: List of log contents to classify
        batch_size: Batch size for processing

    Returns:
        List of predicted classifications
    """
    predictions = []

    for i in range(0, len(log_contents), batch_size):
        batch_contents = log_contents[i:i + batch_size]

        try:
            # Try batch processing first
            batch_predictions = _process_batch(model, tokenizer, batch_contents)
            predictions.extend(batch_predictions)
        except (SystemError, RuntimeError, Exception) as e:
            # If batch processing fails, fall back to individual processing
            print(f"Batch processing failed (batch {i//batch_size + 1}), falling back to individual processing: {str(e)[:100]}")
            for content in batch_contents:
                try:
                    pred = _predict_single(model, tokenizer, content)
                    predictions.append(pred)
                except Exception as single_e:
                    print(f"Warning: Failed to predict single sample, defaulting to 'normal': {str(single_e)[:50]}")
                    predictions.append('normal')

    return predictions

def _process_batch(model, tokenizer, batch_contents: List[str]) -> List[str]:
    """
    Process a batch of contents (internal function)
    """
    batch_prompts = [create_prompt(content) for content in batch_contents]

    # Tokenize batch
    inputs = tokenizer(
        batch_prompts,
        return_tensors="pt",
        truncation=True,
        max_length=Config.MAX_SEQ_LENGTH,
        padding=True
    )

    # Move to device
    if torch.cuda.is_available():
        inputs = {k: v.cuda() for k, v in inputs.items()}

    # Generate responses for the batch
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=10,
            temperature=0.1,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id,
        )

    # Decode responses
    predictions = []
    for j, output in enumerate(outputs):
        response = tokenizer.decode(output, skip_special_tokens=True)
        prompt_len = len(batch_prompts[j])
        response_part = response[prompt_len:].strip().lower()

        if 'normal' in response_part:
            predictions.append('normal')
        elif 'anomaly' in response_part:
            predictions.append('anomaly')
        else:
            # Default to normal if unclear
            predictions.append('normal')

    return predictions

def _predict_single(model, tokenizer, log_content: str) -> str:
    """
    Predict a single log entry (internal function)
    """
    prompt = create_prompt(log_content)

    # Tokenize input
    inputs = tokenizer(
        prompt,
        return_tensors="pt",
        truncation=True,
        max_length=Config.MAX_SEQ_LENGTH,
        padding=True
    )

    # Move to device
    if torch.cuda.is_available():
        inputs = {k: v.cuda() for k, v in inputs.items()}

    # Generate response
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=10,
            temperature=0.1,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id,
        )

    # Decode response
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)

    # Extract prediction from response
    response_part = response[len(prompt):].strip().lower()

    if 'normal' in response_part:
        return 'normal'
    elif 'anomaly' in response_part:
        return 'anomaly'
    else:
        # Default to normal if unclear
        return 'normal'

def evaluate_model(model, tokenizer, test_samples: List[Tuple[str, str]], batch_size: int = 8) -> Dict:
    """
    Evaluate the model on test samples using efficient batch processing

    Args:
        model: Trained model
        tokenizer: Tokenizer
        test_samples: List of (label, content) tuples
        batch_size: Batch size for processing

    Returns:
        Dictionary with evaluation metrics
    """
    print(f"Evaluating model on {len(test_samples)} samples with batch size {batch_size}...")

    # Extract labels and contents
    true_labels = [label for label, _ in test_samples]
    log_contents = [content for _, content in test_samples]

    # Predict in batches for efficiency with error handling
    try:
        predicted_labels = predict_batch(model, tokenizer, log_contents, batch_size)
    except Exception as e:
        print(f"Batch processing failed, falling back to smaller batch size: {str(e)[:100]}")
        try:
            predicted_labels = predict_batch(model, tokenizer, log_contents, batch_size=1)
        except Exception as e2:
            print(f"All batch processing failed, using individual predictions: {str(e2)[:100]}")
            predicted_labels = []
            for i, content in enumerate(log_contents):
                if i % 100 == 0:
                    print(f"Processing sample {i+1}/{len(log_contents)}")
                try:
                    pred = _predict_single(model, tokenizer, content)
                    predicted_labels.append(pred)
                except Exception:
                    predicted_labels.append('normal')  # Default fallback

    # Calculate metrics
    accuracy = accuracy_score(true_labels, predicted_labels)
    precision, recall, f1, _ = precision_recall_fscore_support(
        true_labels, predicted_labels, average='weighted'
    )

    # Confusion matrix
    cm = confusion_matrix(true_labels, predicted_labels, labels=['normal', 'anomaly'])

    results = {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'confusion_matrix': cm.tolist(),
        'num_samples': len(test_samples)
    }

    print("\nEvaluation Results:")
    print(f"Accuracy: {accuracy:.4f}")
    print(f"Precision: {precision:.4f}")
    print(f"Recall: {recall:.4f}")
    print(f"F1 Score: {f1:.4f}")
    print(f"Confusion Matrix:")
    print(f"                Predicted")
    print(f"Actual    Normal  Anomaly")
    print(f"Normal    {cm[0][0]:6d}  {cm[0][1]:7d}")
    print(f"Anomaly   {cm[1][0]:6d}  {cm[1][1]:7d}")

    return results

def test_model(model_path: str, num_test_samples: int = None):
    """
    Test a trained model on the held-out test set

    Args:
        model_path: Path to the trained model
        num_test_samples: Number of test samples to use (None = use all saved test samples)
    """
    print("Testing trained model...")

    # Load model
    model, tokenizer = load_trained_model(model_path)

    # Load the saved test samples (to avoid data leakage)
    test_samples_path = os.path.join(model_path, "test_samples.json")

    if os.path.exists(test_samples_path):
        print("Loading held-out test samples...")
        with open(test_samples_path, "r") as f:
            test_samples = json.load(f)

        # Limit number of test samples if specified
        if num_test_samples is not None and num_test_samples < len(test_samples):
            test_samples = random.sample(test_samples, num_test_samples)
            print(f"Using {num_test_samples} out of {len(test_samples)} available test samples")

        print(f"Using {len(test_samples)} test samples from held-out set")
    else:
        print("WARNING: No saved test samples found. This may cause data leakage!")
        print("Falling back to random sampling (not recommended for final evaluation)")
        if num_test_samples is None:
            num_test_samples = 1000
        test_samples = load_and_sample_data(Config.DATASET_PATH, num_test_samples)

    # Evaluate model with batch processing
    results = evaluate_model(model, tokenizer, test_samples, batch_size=Config.EVAL_BATCH_SIZE)

    # Save results
    results_path = os.path.join(model_path, "evaluation_results.json")
    with open(results_path, "w") as f:
        json.dump(results, f, indent=2)

    print(f"Evaluation results saved to {results_path}")

    return results

def interactive_test(model_path: str):
    """
    Interactive testing mode for manual log entry classification

    Args:
        model_path: Path to the trained model
    """
    print("Loading model for interactive testing...")
    model, tokenizer = load_trained_model(model_path)

    print("\nInteractive Log Classification")
    print("Enter log entries to classify (type 'quit' to exit):")
    print("-" * 50)

    while True:
        log_entry = input("\nEnter log entry: ").strip()

        if log_entry.lower() in ['quit', 'exit', 'q']:
            break

        if not log_entry:
            continue

        prediction = predict_log_entry(model, tokenizer, log_entry)
        print(f"Classification: {prediction}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        command = sys.argv[1]

        if command == "train":
            main()
        elif command == "test":
            model_path = sys.argv[2] if len(sys.argv) > 2 else Config.OUTPUT_DIR
            num_samples = int(sys.argv[3]) if len(sys.argv) > 3 else 1000
            test_model(model_path, num_samples)
        elif command == "interactive":
            model_path = sys.argv[2] if len(sys.argv) > 2 else Config.OUTPUT_DIR
            interactive_test(model_path)
        else:
            print("Usage:")
            print("  python thunderbird_log_classification.py train")
            print("  python thunderbird_log_classification.py test [model_path] [num_samples]")
            print("  python thunderbird_log_classification.py interactive [model_path]")
    else:
        main()
