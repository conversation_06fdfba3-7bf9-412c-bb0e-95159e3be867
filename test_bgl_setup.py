#!/usr/bin/env python3
"""
Test script to verify the BGL setup and basic functionality
"""

import os
import sys
import torch
from bgl_log_classification import (
    Config, parse_log_line, create_prompt, load_and_sample_data
)

def test_dependencies():
    """Test if all required dependencies are installed"""
    print("Testing dependencies...")
    
    try:
        import torch
        print(f"✓ PyTorch {torch.__version__}")
        
        import transformers
        print(f"✓ Transformers {transformers.__version__}")
        
        import datasets
        print(f"✓ Datasets {datasets.__version__}")
        
        import unsloth
        print("✓ Unsloth")
        
        import sklearn
        print(f"✓ Scikit-learn {sklearn.__version__}")
        
        import pandas
        print(f"✓ Pandas {pandas.__version__}")
        
        import numpy
        print(f"✓ NumPy {numpy.__version__}")
        
        return True
        
    except ImportError as e:
        print(f"✗ Missing dependency: {e}")
        return False

def test_cuda():
    """Test CUDA availability"""
    print("\nTesting CUDA...")
    
    if torch.cuda.is_available():
        print(f"✓ CUDA available")
        print(f"  Device count: {torch.cuda.device_count()}")
        print(f"  Current device: {torch.cuda.current_device()}")
        print(f"  Device name: {torch.cuda.get_device_name()}")
        print(f"  Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        return True
    else:
        print("⚠ CUDA not available - training will be slower on CPU")
        return False

def test_dataset():
    """Test dataset loading"""
    print("\nTesting dataset...")
    
    if not os.path.exists(Config.DATASET_PATH):
        print(f"✗ Dataset not found at {Config.DATASET_PATH}")
        return False
    
    print(f"✓ Dataset found at {Config.DATASET_PATH}")
    
    # Test file size
    file_size = os.path.getsize(Config.DATASET_PATH) / (1024**2)  # MB
    print(f"  File size: {file_size:.1f} MB")
    
    return True

def test_log_parsing():
    """Test log parsing functionality"""
    print("\nTesting log parsing...")
    
    # Test normal log
    normal_log = "- 1117838570 2005.06.03 R02-M1-N0-C:J12-U11 2005-06-03-15.42.50.363779 R02-M1-N0-C:J12-U11 RAS KERNEL INFO instruction cache parity error corrected"
    label, content = parse_log_line(normal_log)
    
    if label == 'normal':
        print("✓ Normal log parsing works")
        print(f"  Clean content: {content[:80]}...")
    else:
        print(f"✗ Normal log parsing failed: got {label}")
        return False
    
    # Test anomaly log
    anomaly_log = "APPREAD 1117869872 2005.06.04 R23-M1-N8-I:J18-U11 2005-06-04-00.24.32.398284 R23-M1-N8-I:J18-U11 RAS APP FATAL ciod: failed to read message prefix on control stream"
    label, content = parse_log_line(anomaly_log)
    
    if label == 'anomaly':
        print("✓ Anomaly log parsing works")
        print(f"  Clean content: {content[:80]}...")
    else:
        print(f"✗ Anomaly log parsing failed: got {label}")
        return False
    
    return True

def test_prompt_creation():
    """Test prompt creation"""
    print("\nTesting prompt creation...")
    
    content = "Test BGL log entry"
    prompt = create_prompt(content)
    
    if "Test BGL log entry" in prompt and "Classification:" in prompt:
        print("✓ Prompt creation works")
        return True
    else:
        print("✗ Prompt creation failed")
        return False

def test_small_sample():
    """Test loading a small sample of data"""
    print("\nTesting small data sample...")
    
    if not os.path.exists(Config.DATASET_PATH):
        print("⚠ Skipping - dataset not available")
        return True
    
    try:
        # Load a very small sample
        samples = load_and_sample_data(Config.DATASET_PATH, 100)
        
        if len(samples) > 0:
            print(f"✓ Successfully loaded {len(samples)} samples")
            
            # Check distribution
            normal_count = sum(1 for label, _ in samples if label == 'normal')
            anomaly_count = len(samples) - normal_count
            print(f"  Normal: {normal_count}, Anomaly: {anomaly_count}")
            
            # Show sample entries
            print("  Sample entries:")
            for i, (label, content) in enumerate(samples[:3]):
                print(f"    {i+1}. [{label}] {content[:60]}...")
            
            return True
        else:
            print("✗ No samples loaded")
            return False
            
    except Exception as e:
        print(f"✗ Error loading samples: {e}")
        return False

def compare_datasets():
    """Compare BGL and Thunderbird datasets"""
    print("\nComparing BGL vs Thunderbird datasets...")

    bgl_path = "datasets/BGL/BGL.log"
    thunderbird_path = "datasets/Thunderbird/Thunderbird_10M.log"

    if os.path.exists(bgl_path):
        bgl_size = os.path.getsize(bgl_path) / (1024**2)  # MB
        print(f"✓ BGL dataset: {bgl_size:.1f} MB")
    else:
        print("✗ BGL dataset not found")

    if os.path.exists(thunderbird_path):
        thunderbird_size = os.path.getsize(thunderbird_path) / (1024**3)  # GB
        print(f"✓ Thunderbird dataset: {thunderbird_size:.1f} GB")
    else:
        print("✗ Thunderbird dataset not found")

    return True

def main():
    """Run all tests"""
    print("BGL Log Classification Setup Test")
    print("=" * 50)
    
    tests = [
        test_dependencies,
        test_cuda,
        test_dataset,
        test_log_parsing,
        test_prompt_creation,
        test_small_sample,
        compare_datasets,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test failed with exception: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("✓ All tests passed! Ready to start training.")
    else:
        print("⚠ Some tests failed. Please check the issues above.")
        
        if not results[0]:  # Dependencies failed
            print("\nTo install dependencies, run:")
            print("pip install -r requirements.txt")
        
        if not results[2]:  # Dataset not found
            print(f"\nPlease ensure the BGL dataset is available at:")
            print(f"{Config.DATASET_PATH}")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
