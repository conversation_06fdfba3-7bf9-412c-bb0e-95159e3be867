# Thunderbird Log Anomaly Classification with Unsloth

This project implements a fine-tuned language model for classifying log entries from the Thunderbird supercomputer dataset as either normal or anomalous using the Unsloth framework.

## Dataset

The Thunderbird dataset contains logs from a supercomputer system at Sandia National Labs with:
- **Total entries**: 10,000,000 log entries
- **Normal logs**: ~96.5% (entries starting with "-")
- **Anomalous logs**: ~3.5% (entries starting with error codes like ECC, VAPI, etc.)

## Features

- **Efficient Fine-tuning**: Uses Unsloth for fast and memory-efficient training
- **Random Sampling**: Intelligently samples from the large dataset for training
- **Balanced Evaluation**: Proper train/validation splits with comprehensive metrics
- **Multiple Modes**: Training, testing, and interactive classification modes
- **LoRA Adaptation**: Uses Low-Rank Adaptation for efficient parameter updates

## Installation

1. Clone this repository
2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Training

Train the model on the Thunderbird dataset:

```bash
python thunderbird_log_classification.py train
```

This will:
- Sample 100,000 log entries from the dataset
- Split into 80% training, 20% validation
- Fine-tune the model using LoRA
- Save the trained model to `./thunderbird_log_classifier`

### Testing

Evaluate the trained model:

```bash
python thunderbird_log_classification.py test [model_path] [num_samples]
```

Example:
```bash
python thunderbird_log_classification.py test ./thunderbird_log_classifier 1000
```

### Interactive Testing

Test individual log entries interactively:

```bash
python thunderbird_log_classification.py interactive [model_path]
```

## Configuration

Key configuration parameters in the `Config` class:

- `SAMPLE_SIZE`: Number of samples for training (default: 100,000)
- `MODEL_NAME`: Base model to fine-tune (default: "unsloth/llama-3-8b-bnb-4bit")
- `MAX_SEQ_LENGTH`: Maximum sequence length (default: 512)
- `NUM_TRAIN_EPOCHS`: Training epochs (default: 3)
- `LEARNING_RATE`: Learning rate (default: 2e-4)

## Model Architecture

- **Base Model**: Llama-3-8B with 4-bit quantization
- **Fine-tuning**: LoRA (Low-Rank Adaptation) with rank 16
- **Target Modules**: All attention and MLP projection layers
- **Training**: Supervised fine-tuning with classification prompts

## Example Log Classifications

**Normal Log Entry:**
```
Input: "1131523501 2005.11.09 aadmin1 Nov 10 00:05:01 src@aadmin1 in.tftpd[14620]: tftp: client does not accept options"
Output: normal
```

**Anomalous Log Entry:**
```
Input: "ECC 1131674844 2005.11.10 cn994 Nov 10 18:07:24 cn994/cn994 Server Administrator: Memory device status is critical"
Output: anomaly
```

## Performance Metrics

The model is evaluated using:
- **Accuracy**: Overall classification accuracy
- **Precision**: Precision for both classes
- **Recall**: Recall for both classes
- **F1-Score**: Weighted F1-score
- **Confusion Matrix**: Detailed classification breakdown

## Hardware Requirements

- **GPU**: NVIDIA GPU with at least 8GB VRAM (recommended)
- **RAM**: At least 16GB system RAM
- **Storage**: ~5GB for model and dataset

## File Structure

```
.
├── thunderbird_log_classification.py  # Main training script
├── requirements.txt                   # Python dependencies
├── README.md                         # This file
├── datasets/
│   └── Thunderbird/
│       └── Thunderbird_10M.log      # Dataset file
└── thunderbird_log_classifier/       # Output directory (created after training)
    ├── adapter_config.json
    ├── adapter_model.safetensors
    ├── config.json
    ├── tokenizer.json
    └── evaluation_results.json
```

## Troubleshooting

1. **CUDA Out of Memory**: Reduce `PER_DEVICE_TRAIN_BATCH_SIZE` or `MAX_SEQ_LENGTH`
2. **Slow Training**: Ensure you have a compatible GPU and CUDA installed
3. **Import Errors**: Make sure all dependencies are installed correctly

## Citation

If you use this code or the Thunderbird dataset, please cite:

```bibtex
@inproceedings{oliner2007supercomputers,
  title={What supercomputers say: A study of five system logs},
  author={Oliner, Adam J and Stearley, Jon},
  booktitle={37th Annual IEEE/IFIP International Conference on Dependable Systems and Networks (DSN'07)},
  pages={575--584},
  year={2007},
  organization={IEEE}
}
```
