#!/usr/bin/env python3
"""
Performance test script to demonstrate the improvement from batch processing
"""

import time
import random
from typing import List, Tuple
from bgl_log_classification import (
    Config, 
    create_train_val_test_split, 
    load_trained_model,
    predict_batch,
    evaluate_model
)

def simulate_old_evaluation(model, tokenizer, test_samples: List[Tuple[str, str]]) -> float:
    """
    Simulate the old one-by-one evaluation approach for timing comparison
    """
    print("Simulating old evaluation approach (one-by-one)...")
    start_time = time.time()
    
    # Simulate processing each sample individually
    for i, (_, content) in enumerate(test_samples):
        if i % 50 == 0:
            print(f"Processing sample {i+1}/{len(test_samples)}")
        
        # Simulate the old predict_log_entry function
        predictions = predict_batch(model, tokenizer, [content], batch_size=1)
    
    end_time = time.time()
    return end_time - start_time

def test_batch_evaluation(model, tokenizer, test_samples: List[Tuple[str, str]], batch_size: int = 16) -> float:
    """
    Test the new batch evaluation approach
    """
    print(f"Testing new batch evaluation approach (batch_size={batch_size})...")
    start_time = time.time()
    
    # Use the new batch evaluation
    results = evaluate_model(model, tokenizer, test_samples, batch_size=batch_size)
    
    end_time = time.time()
    return end_time - start_time, results

def main():
    """Main performance test function"""
    print("Performance Test: Batch Processing vs One-by-One")
    print("=" * 60)
    
    # Check if we have a trained model
    model_path = "./bgl_log_classifier"
    try:
        print("Loading trained model...")
        model, tokenizer = load_trained_model(model_path)
        print("✅ Model loaded successfully!")
    except Exception as e:
        print(f"❌ Could not load model from {model_path}")
        print(f"Error: {e}")
        print("Please train a model first using: python bgl_log_classification.py train")
        return
    
    # Create test data
    print("\nCreating test dataset...")
    _, _, test_samples = create_train_val_test_split(
        Config.DATASET_PATH,
        total_sample_size=500,  # Smaller sample for performance testing
        train_ratio=0.8,
        val_ratio=0.1,
        test_ratio=0.1
    )
    
    print(f"Using {len(test_samples)} test samples for performance comparison")
    
    # Test different batch sizes
    batch_sizes = [1, 4, 8, 16, 32]
    
    print("\n" + "="*60)
    print("PERFORMANCE COMPARISON")
    print("="*60)
    
    results = {}
    
    for batch_size in batch_sizes:
        print(f"\nTesting batch size: {batch_size}")
        try:
            eval_time, eval_results = test_batch_evaluation(model, tokenizer, test_samples, batch_size)
            results[batch_size] = {
                'time': eval_time,
                'accuracy': eval_results['accuracy']
            }
            print(f"Time: {eval_time:.2f}s, Accuracy: {eval_results['accuracy']:.4f}")
        except Exception as e:
            print(f"Error with batch size {batch_size}: {e}")
    
    # Show results summary
    print("\n" + "="*60)
    print("RESULTS SUMMARY")
    print("="*60)
    print(f"{'Batch Size':<12} {'Time (s)':<10} {'Accuracy':<10} {'Speedup':<10}")
    print("-" * 50)
    
    baseline_time = results[1]['time'] if 1 in results else None
    
    for batch_size in sorted(results.keys()):
        time_taken = results[batch_size]['time']
        accuracy = results[batch_size]['accuracy']
        speedup = f"{baseline_time/time_taken:.2f}x" if baseline_time else "N/A"
        print(f"{batch_size:<12} {time_taken:<10.2f} {accuracy:<10.4f} {speedup:<10}")
    
    # Recommendations
    print("\n" + "="*60)
    print("RECOMMENDATIONS")
    print("="*60)
    
    if results:
        best_batch_size = min(results.keys(), key=lambda x: results[x]['time'])
        best_time = results[best_batch_size]['time']
        worst_time = results[1]['time'] if 1 in results else max(results.values(), key=lambda x: x['time'])['time']
        
        print(f"✅ Best performing batch size: {best_batch_size}")
        print(f"✅ Best time: {best_time:.2f}s")
        print(f"✅ Speedup over batch_size=1: {worst_time/best_time:.2f}x")
        print(f"\n💡 For evaluation, use batch_size={best_batch_size} or higher")
        print(f"💡 Current default (Config.EVAL_BATCH_SIZE={Config.EVAL_BATCH_SIZE}) is {'good' if Config.EVAL_BATCH_SIZE >= best_batch_size else 'could be improved'}")

if __name__ == "__main__":
    main()
